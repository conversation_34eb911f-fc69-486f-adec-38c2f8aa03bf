#!/usr/bin/env python3
"""
LOOP EVOLVE USAGE GUIDE
======================

This script demonstrates how to use the autonomous Loop Evolve system
for algorithm discovery and optimization.
"""

import asyncio
import logging
import yaml
from datetime import datetime
import json
from pathlib import Path

from autonomous_loop_evolve import AutonomousLoopEvolve

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('loop_evolve_usage.log')
    ]
)
logger = logging.getLogger(__name__)

async def demonstrate_usage():
    """Demonstrate how to use the Loop Evolve system"""
    
    print("\n🚀 Loop Evolve System Usage Guide")
    print("=" * 50)
    
    # 1. Basic Usage
    print("\n1. Basic Usage:")
    print("   - Simply run: python autonomous_loop_evolve.py")
    print("   - The system will start autonomously")
    print("   - It will save results in the 'autonomous_results' directory")
    
    # 2. Configuration
    print("\n2. Configuration (config.yaml):")
    print("   - LLM Settings:")
    print("     * Uses Gemini 2.5 Flash model")
    print("     * 25 requests per day")
    print("     * 250,000 tokens per request")
    print("   - Evolution Parameters:")
    print("     * Population size: 50")
    print("     * Generations: 100")
    print("     * Selection rate: 0.2")
    print("     * Mutation rate: 0.1")
    print("     * Crossover rate: 0.8")
    
    # 3. Problem Domains
    print("\n3. Problem Domains:")
    print("   - Algorithm Optimization")
    print("     * Performance and efficiency")
    print("     * Memory usage optimization")
    print("     * Accuracy maintenance")
    print("   - Code Generation")
    print("     * Code quality")
    print("     * Complexity management")
    print("     * Maintainability")
    print("   - Resource Management")
    print("     * CPU usage optimization")
    print("     * Memory efficiency")
    print("     * Throughput improvement")
    
    # 4. Monitoring
    print("\n4. Monitoring:")
    print("   - Real-time statistics in loop_evolve_usage.log")
    print("   - Checkpoints in autonomous_results/")
    print("   - Performance metrics:")
    print("     * Fitness scores")
    print("     * Population diversity")
    print("     * Success rates")
    print("     * Resource usage")
    
    # 5. Results
    print("\n5. Results:")
    print("   - Best solutions in autonomous_results/")
    print("   - Pareto front of optimal solutions")
    print("   - Performance history")
    print("   - Parameter adaptation history")
    
    # 6. Example Run
    print("\n6. Starting Example Run:")
    print("   - Initializing system...")
    
    try:
        # Initialize system
        system = AutonomousLoopEvolve()
        await system.initialize()
        
        # Run for a few generations
        print("\nRunning 5 generations as example...")
        for gen in range(5):
            await system.controller.evolve_generation()
            best_program = system.controller.get_best_program()
            if best_program:
                print(f"\nGeneration {gen + 1}:")
                print(f"Best Fitness: {best_program.fitness:.4f}")
                print("Best Program:")
                print("-" * 40)
                print(best_program.code)
                print("-" * 40)
        
        # Show final results
        print("\nExample Run Complete!")
        print("\nFinal Results:")
        print(f"Best Fitness: {system.monitor.best_fitness:.4f}")
        print(f"Population Size: {system.monitor.population_stats['size']}")
        print(f"Success Rate: {system.monitor.evolution_stats['successful_mutations'] / (system.monitor.evolution_stats['successful_mutations'] + system.monitor.evolution_stats['failed_mutations']):.2%}")
        
    except Exception as e:
        print(f"\nError during example run: {e}")
    
    # 7. Tips
    print("\n7. Tips for Best Results:")
    print("   - Let the system run for multiple generations")
    print("   - Monitor the logs for progress")
    print("   - Check the Pareto front for diverse solutions")
    print("   - Use checkpoints to resume evolution")
    print("   - Adjust config.yaml for specific needs")
    
    # 8. Advanced Usage
    print("\n8. Advanced Usage:")
    print("   - Custom problem domains:")
    print("     * Add new domains in _initialize_problem_domains()")
    print("     * Define custom metrics and constraints")
    print("   - Parameter tuning:")
    print("     * Adjust evolution parameters in config.yaml")
    print("     * Modify objective weights")
    print("     * Set custom constraints")
    print("   - Multi-objective optimization:")
    print("     * Define custom objectives")
    print("     * Adjust Pareto front criteria")
    print("     * Set custom dominance rules")

async def main():
    """Main function"""
    await demonstrate_usage()

if __name__ == "__main__":
    asyncio.run(main()) 