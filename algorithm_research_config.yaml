algorithm_templates:
  compression: "\ndef compress_tensor(tensor, target_ratio=10):\n    '''\n    Compress\
    \ tensor with target compression ratio.\n    \n    Args:\n        tensor: Input\
    \ tensor to compress\n        target_ratio: Target compression ratio\n       \
    \ \n    Returns:\n        dict: {\n            'compressed_data': compressed representation,\n\
    \            'compression_ratio': actual ratio achieved,\n            'memory_usage':\
    \ memory used in MB,\n            'quality_score': quality preservation score\n\
    \        }\n    '''\n    # Your algorithm implementation here\n    pass\n\ndef\
    \ evaluate():\n    '''Evaluation function for the algorithm'''\n    import torch\n\
    \    import numpy as np\n    \n    # Test with sample tensors\n    test_tensors\
    \ = [\n        torch.randn(1000, 1000),\n        torch.randn(500, 2000),\n   \
    \     torch.randn(100, 100, 100)\n    ]\n    \n    results = []\n    for tensor\
    \ in test_tensors:\n        result = compress_tensor(tensor)\n        results.append(result)\n\
    \    \n    # Calculate metrics\n    avg_ratio = np.mean([r['compression_ratio']\
    \ for r in results])\n    avg_memory = np.mean([r['memory_usage'] for r in results])\n\
    \    avg_quality = np.mean([r['quality_score'] for r in results])\n    \n    return\
    \ {\n        'fitness': avg_ratio * avg_quality / max(avg_memory, 1),\n      \
    \  'compression_ratio': avg_ratio,\n        'memory_efficiency': 1000 / max(avg_memory,\
    \ 1),\n        'quality_preservation': avg_quality\n    }\n"
  optimization: "\ndef optimization_algorithm(objective_function, constraints=None):\n\
    \    '''\n    General optimization algorithm.\n    \n    Args:\n        objective_function:\
    \ Function to optimize\n        constraints: Optional constraints\n        \n\
    \    Returns:\n        dict: Optimization results\n    '''\n    # Your optimization\
    \ algorithm here\n    pass\n\ndef evaluate():\n    '''Evaluation function for\
    \ optimization algorithm'''\n    import numpy as np\n    \n    # Test optimization\
    \ problems\n    def test_function(x):\n        return -(x[0]**2 + x[1]**2)  #\
    \ Maximize negative quadratic\n    \n    result = optimization_algorithm(test_function)\n\
    \    \n    return {\n        'fitness': -result.get('best_value', -1000),\n  \
    \      'convergence_speed': result.get('iterations', 1000) / 1000,\n        'solution_quality':\
    \ result.get('quality', 0),\n        'robustness': result.get('robustness', 0)\n\
    \    }\n"
  streaming: "\ndef streaming_algorithm(data_stream, cache_size=100):\n    '''\n \
    \   Process data stream with limited memory.\n    \n    Args:\n        data_stream:\
    \ Iterator of data chunks\n        cache_size: Maximum cache size in MB\n    \
    \    \n    Returns:\n        dict: Processing results and metrics\n    '''\n \
    \   # Your streaming algorithm here\n    pass\n\ndef evaluate():\n    '''Evaluation\
    \ function for streaming algorithm'''\n    import torch\n    \n    # Simulate\
    \ data stream\n    def data_generator():\n        for i in range(100):\n     \
    \       yield torch.randn(1000, 1000)\n    \n    result = streaming_algorithm(data_generator())\n\
    \    \n    return {\n        'fitness': result.get('throughput', 0) * result.get('efficiency',\
    \ 0),\n        'memory_efficiency': result.get('memory_efficiency', 0),\n    \
    \    'processing_speed': result.get('throughput', 0),\n        'scalability':\
    \ result.get('scalability', 0)\n    }\n"
evaluation:
  max_memory_mb: 1024
  metrics:
  - compression_ratio
  - memory_efficiency
  - processing_speed
  - quality_preservation
  - scalability
  safety_checks: true
  timeout_seconds: 60
evolution:
  crossover_rate: 0.5
  elite_preservation: 0.1
  generations: 100
  mutation_rate: 0.7
  population_size: 20
  selection_rate: 0.3
llm:
  default_provider: gemini
  providers:
    gemini:
      max_tokens: 8192
      model: gemini-2.0-flash-exp
      temperature: 0.7
prompt_templates:
  evolution: '

    Improve this algorithm based on the following best performing algorithms:


    {best_programs}


    Task: {task_description}

    Focus: {focus_area}


    Create a better version that combines the best aspects and adds novel improvements.

    Must follow the template structure and include evaluate() function.

    '
  initial: '

    Create a novel {algorithm_type} algorithm that {task_description}.


    Requirements:

    - Must be implementable in Python

    - Should be memory efficient

    - Must include proper evaluation metrics

    - Focus on {focus_area}


    Template to follow:

    {template}


    Make the algorithm innovative and efficient.

    '
  refinement: "\nRefine this algorithm to better meet these specific requirements:\n\
    \n{constraints}\n\nCurrent algorithm:\n{current_algorithm}\n\nOptimize for:\n\
    - Higher compression ratios\n- Lower memory usage  \n- Better quality preservation\n\
    - Faster processing\n\nKeep the same interface but improve the implementation.\n"
research_focus: algorithm_optimization
target_domains:
- compression_algorithms
- streaming_weights
- quantization_methods
- neural_architecture_search
- optimization_algorithms
- memory_efficient_algorithms
