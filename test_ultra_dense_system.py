#!/usr/bin/env python3
"""
🔥 ULTRA-DENSE RESEARCH SYSTEM TEST
==================================

Quick test to verify the ultra-dense research system is working
before starting the full research campaign.
"""

import asyncio
import yaml
import json
from pathlib import Path
from ultra_dense_researcher import UltraDenseResearcher

async def test_system():
    """Test the ultra-dense research system"""
    
    print("🔥 TESTING ULTRA-DENSE RESEARCH SYSTEM")
    print("=" * 45)
    
    # Initialize researcher
    try:
        researcher = UltraDenseResearcher()
        print("✅ Researcher initialized")
    except Exception as e:
        print(f"❌ Failed to initialize researcher: {e}")
        return False
    
    # Test configuration loading
    try:
        config = researcher.config
        target_ratio = config.get('target_ratio', 0)
        domains = list(config.get('research_domains', {}).keys())
        print(f"✅ Configuration loaded")
        print(f"   Target ratio: {target_ratio:,}x")
        print(f"   Research domains: {len(domains)}")
        print(f"   Domains: {', '.join(domains)}")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False
    
    # Test directory creation
    try:
        required_dirs = [
            researcher.results_dir,
            researcher.logs_dir, 
            researcher.algorithms_dir
        ]
        
        for directory in required_dirs:
            if directory.exists():
                print(f"✅ Directory exists: {directory}")
            else:
                print(f"❌ Directory missing: {directory}")
                return False
    except Exception as e:
        print(f"❌ Directory check failed: {e}")
        return False
    
    # Test mock algorithm evaluation
    try:
        mock_algorithm = researcher._generate_mock_algorithm()
        metrics = researcher.evaluate_algorithm(mock_algorithm)
        
        print(f"✅ Algorithm evaluation test")
        print(f"   Fitness: {metrics.get('fitness', 0):.4f}")
        print(f"   Compression ratio: {metrics.get('compression_ratio', 0):.1f}x")
        print(f"   Data integrity: {metrics.get('data_integrity', 0):.2f}")
        
    except Exception as e:
        print(f"❌ Algorithm evaluation failed: {e}")
        return False
    
    # Test single generation (mock)
    try:
        print(f"\n🧪 Testing single research generation...")
        
        # Test with just one domain to avoid API calls
        test_domains = [domains[0]] if domains else ['mathematics']
        
        # This will use mock responses
        summary = await researcher.research_generation(test_domains)
        
        print(f"✅ Generation test complete")
        print(f"   Best fitness: {summary.get('best_fitness', 0):.4f}")
        print(f"   Best compression: {summary.get('best_compression_ratio', 0):.1f}x")
        print(f"   API calls: {summary.get('api_calls_made', 0)}")
        
    except Exception as e:
        print(f"❌ Generation test failed: {e}")
        return False
    
    print(f"\n🎯 SYSTEM TEST RESULTS:")
    print(f"✅ All tests passed - system ready for research!")
    print(f"\nNext steps:")
    print(f"1. Set GOOGLE_API_KEY environment variable for real API calls")
    print(f"2. Run: python ultra_dense_researcher.py")
    print(f"3. Monitor: ultra_dense_research/logs/")
    print(f"4. Results: ultra_dense_research/results/")
    
    return True

async def quick_research_demo():
    """Run a quick 3-generation demo"""
    
    print(f"\n🚀 RUNNING QUICK RESEARCH DEMO (3 generations)")
    print("=" * 50)
    
    researcher = UltraDenseResearcher()
    
    # Run just 3 generations for demo
    await researcher.run_ultra_dense_research(target_generations=3)
    
    print(f"\n📊 DEMO COMPLETE - Check results in ultra_dense_research/")

if __name__ == "__main__":
    async def main():
        # Test system first
        system_ok = await test_system()
        
        if system_ok:
            # Run quick demo
            await quick_research_demo()
        else:
            print("❌ System test failed - fix issues before running research")
    
    asyncio.run(main())
