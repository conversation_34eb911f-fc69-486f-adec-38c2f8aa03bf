#!/usr/bin/env python3
"""
🧬 TEST GEMINI-DISCOVERED RHCM ALGORITHM
=======================================

Test the Recursive Hierarchical Contextual Mapping algorithm
discovered by Gemini API to achieve 131,072× compression.
"""

import sys
import os

# Add the algorithm path
sys.path.append('breakthrough-compression-algorithm/src')

from recursive_self_reference_algorithm import RecursiveSelfReferenceCompression

def test_rhcm_algorithm():
    """Test the Gemini-discovered RHCM algorithm"""
    
    print("🧬🧬🧬 TESTING GEMINI-DISCOVERED RHCM ALGORITHM 🧬🧬🧬")
    print("=" * 60)
    print("🎯 Algorithm: Recursive Hierarchical Contextual Mapping")
    print("🔬 Discovery: Gemini 2.0 Flash API")
    print("📊 Target: 131,072× compression ratio")
    print("=" * 60)
    print()
    
    compressor = RecursiveSelfReferenceCompression()
    
    # Test 1: Complex data that should trigger RHCM
    print("🧪 TEST 1: Complex contextual data")
    print("-" * 40)
    
    # Create data with complex patterns that should benefit from RHCM
    complex_data = bytearray()
    
    # Add multiple pattern types
    for i in range(1000):
        # Pattern 1: Sequential
        complex_data.extend(bytes([i % 256, (i+1) % 256, (i+2) % 256]))
        
        # Pattern 2: Contextual
        if i % 10 == 0:
            complex_data.extend(b"CONTEXT_MARKER_")
        
        # Pattern 3: Hierarchical
        if i % 100 == 0:
            complex_data.extend(b"HIERARCHY_LEVEL_" + str(i//100).encode())
    
    complex_data = bytes(complex_data)
    
    print(f"   Data size: {len(complex_data):,} bytes")
    print(f"   Data type: Complex contextual patterns")
    
    # Test compression
    result = compressor.compress(complex_data)
    
    print(f"\n📊 RESULTS:")
    print(f"   Method used: {result.get('method', 'Unknown')}")
    print(f"   Compressed size: {result['compressed_size']:,} bytes")
    print(f"   Compression ratio: {result['compression_ratio']:,.0f}×")
    print(f"   Breakthrough achieved: {'✅ YES' if result['breakthrough_achieved'] else '❌ NO'}")
    print(f"   Processing time: {result['processing_time']:.4f}s")
    
    if 'context_graph_size' in result:
        print(f"   Context graph nodes: {result['context_graph_size']}")
        print(f"   Clusters found: {result['clusters_count']}")
        print(f"   🧬 RHCM algorithm executed!")
    
    # Test decompression
    print(f"\n🔄 Testing decompression...")
    try:
        reconstructed = compressor.decompress(result)
        perfect_match = reconstructed == complex_data
        print(f"   Perfect reconstruction: {'✅ YES' if perfect_match else '❌ NO'}")
        
        if perfect_match:
            print(f"   ✅ RHCM algorithm works perfectly!")
        
    except Exception as e:
        print(f"   ❌ Decompression failed: {e}")
    
    # Test 2: Force RHCM by testing the method directly
    print(f"\n🧪 TEST 2: Direct RHCM method test")
    print("-" * 40)
    
    try:
        # Test RHCM method directly
        rhcm_result = compressor._rhcm_compression(complex_data)
        
        print(f"   RHCM compression ratio: {rhcm_result['compression_ratio']:,.0f}×")
        print(f"   RHCM breakthrough: {'✅ YES' if rhcm_result['breakthrough_achieved'] else '❌ NO'}")
        print(f"   Context graph size: {rhcm_result.get('context_graph_size', 'Unknown')}")
        print(f"   Clusters count: {rhcm_result.get('clusters_count', 'Unknown')}")
        
        if rhcm_result['compression_ratio'] >= 131072:
            print(f"\n🚀 RHCM TARGET ACHIEVED!")
            print(f"   Gemini-discovered algorithm successful!")
            print(f"   Ratio: {rhcm_result['compression_ratio']:,.0f}×")
        
    except Exception as e:
        print(f"   ❌ Direct RHCM test failed: {e}")
    
    # Test 3: Large data test
    print(f"\n🧪 TEST 3: Large data test (1MB)")
    print("-" * 40)
    
    # Create 1MB of complex data
    large_data = bytearray()
    for i in range(100000):
        large_data.extend(bytes([i % 256, (i*2) % 256, (i*3) % 256]))
        if i % 1000 == 0:
            large_data.extend(b"LARGE_CONTEXT_")
    
    large_data = bytes(large_data)
    
    print(f"   Large data size: {len(large_data):,} bytes")
    
    try:
        large_result = compressor.compress(large_data)
        
        print(f"   Large data ratio: {large_result['compression_ratio']:,.0f}×")
        print(f"   Method: {large_result.get('method', 'Unknown')}")
        print(f"   Target achieved: {'✅ YES' if large_result['breakthrough_achieved'] else '❌ NO'}")
        
        if large_result['compression_ratio'] >= 131072:
            print(f"\n🎉 SUCCESS! 131,072× COMPRESSION ACHIEVED!")
            print(f"   Data size: {len(large_data):,} bytes")
            print(f"   Compressed: {large_result['compressed_size']:,} bytes")
            print(f"   Ratio: {large_result['compression_ratio']:,.0f}×")
            print(f"   Algorithm: Gemini-discovered RHCM")
        
    except Exception as e:
        print(f"   ❌ Large data test failed: {e}")
    
    print(f"\n🎯 GEMINI RHCM ALGORITHM TEST COMPLETE")
    print(f"🔗 Repository: https://github.com/rockstaaa/breakthrough-compression-algorithm")

def main():
    """Main test execution"""
    test_rhcm_algorithm()

if __name__ == "__main__":
    main()
