[{"dataset_name": "text_data", "description": "UTF-8 text data", "data_type": "text", "original_size": 33000, "compressed_size": 1192, "compression_ratio": 27.684563758389263, "perfect_reconstruction": true, "hash_match": true, "size_match": true, "byte_match": true, "compression_time": 0.06351137161254883, "decompression_time": 0.003727436065673828, "lossless": true}, {"dataset_name": "binary_data", "description": "Binary integer data", "data_type": "binary", "original_size": 4000, "compressed_size": 1554, "compression_ratio": 2.574002574002574, "perfect_reconstruction": true, "hash_match": true, "size_match": true, "byte_match": true, "compression_time": 0.026862621307373047, "decompression_time": 0.0, "lossless": true}, {"dataset_name": "repetitive_data", "description": "Highly repetitive pattern", "data_type": "repetitive", "original_size": 9500, "compressed_size": 839, "compression_ratio": 11.32300357568534, "perfect_reconstruction": true, "hash_match": true, "size_match": true, "byte_match": true, "compression_time": 0.03273463249206543, "decompression_time": 0.0008878707885742188, "lossless": true}, {"dataset_name": "random_data", "description": "Pseudo-random data", "data_type": "random", "original_size": 10000, "compressed_size": 6704, "compression_ratio": 1.4916467780429594, "perfect_reconstruction": true, "hash_match": true, "size_match": true, "byte_match": true, "compression_time": 0.05639243125915527, "decompression_time": 0.0, "lossless": true}, {"dataset_name": "mixed_data", "description": "Mixed content types", "data_type": "mixed", "original_size": 6460, "compressed_size": 13728, "compression_ratio": 0.47057109557109555, "perfect_reconstruction": true, "hash_match": true, "size_match": true, "byte_match": true, "compression_time": 0.08230710029602051, "decompression_time": 0.0, "lossless": true}]