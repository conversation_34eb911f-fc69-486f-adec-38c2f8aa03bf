#!/usr/bin/env python3
"""
🔥 MONITOR REAL 1GB COMPRESSION TEST
===================================

Monitor the progress of real 1GB file compression test.
"""

import os
import time
import json

def monitor_1gb_test():
    """Monitor the 1GB test progress"""
    
    print("🔥🔥🔥 MONITORING REAL 1GB COMPRESSION TEST 🔥🔥🔥")
    print("=" * 60)
    
    target_file = "real_1gb_test_file.dat"
    target_size = 1024 * 1024 * 1024  # 1GB
    
    print(f"🔍 Monitoring file: {target_file}")
    print(f"🎯 Target size: {target_size:,} bytes (1.00 GB)")
    print("=" * 60)
    
    start_time = time.time()
    last_size = 0
    
    while True:
        current_time = time.time()
        elapsed = current_time - start_time
        
        # Check if file exists
        if os.path.exists(target_file):
            current_size = os.path.getsize(target_file)
            progress = (current_size / target_size) * 100
            
            # Calculate speed
            size_diff = current_size - last_size
            speed_mbps = (size_diff / 1024 / 1024) / 5 if elapsed > 0 else 0  # 5 second intervals
            
            print(f"\n📊 PROGRESS UPDATE (T+{elapsed:.0f}s):")
            print(f"   Current size: {current_size:,} bytes ({current_size/1024/1024:.1f} MB)")
            print(f"   Progress: {progress:.2f}% of 1GB target")
            print(f"   Write speed: {speed_mbps:.1f} MB/s")
            print(f"   ETA: {((target_size - current_size) / max(1, size_diff)) * 5:.0f}s" if size_diff > 0 else "   ETA: Calculating...")
            
            last_size = current_size
            
            # Check if file creation is complete
            if current_size >= target_size * 0.95:
                print(f"\n✅ FILE CREATION COMPLETE!")
                print(f"   Final size: {current_size:,} bytes ({current_size/1024/1024/1024:.2f} GB)")
                print(f"   Creation time: {elapsed:.2f}s")
                print(f"   Average speed: {(current_size/1024/1024)/elapsed:.1f} MB/s")
                break
        else:
            print(f"\n⏳ WAITING FOR FILE CREATION (T+{elapsed:.0f}s)...")
            print(f"   File: {target_file}")
            print(f"   Status: Not found yet...")
        
        # Check for results files
        result_files = [f for f in os.listdir('.') if f.startswith('real_1gb_compression_results_')]
        if result_files:
            latest_result = max(result_files)
            print(f"\n🎯 COMPRESSION RESULTS DETECTED!")
            print(f"   Results file: {latest_result}")
            
            try:
                with open(latest_result, 'r') as f:
                    results = json.load(f)
                
                compression_ratio = results['compression_results']['compression_ratio']
                target_achieved = results['compression_results']['target_achieved']
                
                print(f"   Compression ratio: {compression_ratio:.2f}×")
                print(f"   Target (131,072×): {'✅ ACHIEVED' if target_achieved else '📊 IN PROGRESS'}")
                print(f"   Progress: {(compression_ratio/131072)*100:.4f}% of target")
                
                if target_achieved:
                    print(f"\n🚀 BREAKTHROUGH ACHIEVED ON REAL 1GB FILE!")
                    break
                    
            except Exception as e:
                print(f"   Error reading results: {e}")
        
        # Wait before next check
        time.sleep(5)
        
        # Safety timeout (30 minutes)
        if elapsed > 1800:
            print(f"\n⏰ TIMEOUT REACHED (30 minutes)")
            print(f"   Current progress: {progress:.2f}% if file exists")
            break
    
    print(f"\n🔍 FINAL STATUS CHECK:")
    
    # Final file check
    if os.path.exists(target_file):
        final_size = os.path.getsize(target_file)
        print(f"   File exists: ✅")
        print(f"   Final size: {final_size:,} bytes ({final_size/1024/1024/1024:.2f} GB)")
        print(f"   Size adequate: {'✅' if final_size >= target_size * 0.95 else '❌'}")
    else:
        print(f"   File exists: ❌")
    
    # Final results check
    result_files = [f for f in os.listdir('.') if f.startswith('real_1gb_compression_results_')]
    if result_files:
        latest_result = max(result_files)
        print(f"   Results available: ✅ ({latest_result})")
        
        try:
            with open(latest_result, 'r') as f:
                results = json.load(f)
            
            print(f"   Compression test: ✅ COMPLETED")
            print(f"   Final ratio: {results['compression_results']['compression_ratio']:.2f}×")
            print(f"   Target achieved: {'✅' if results['compression_results']['target_achieved'] else '❌'}")
            
        except Exception as e:
            print(f"   Results error: {e}")
    else:
        print(f"   Results available: ❌")

if __name__ == "__main__":
    monitor_1gb_test()
