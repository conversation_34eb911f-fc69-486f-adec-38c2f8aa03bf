#!/usr/bin/env python3
"""
🔥 RESEARCH PAPER SETUP
======================

Create comprehensive research paper folder structure for breakthrough compression algorithm
"""

import os
import json
import shutil
from datetime import datetime

def setup_research_paper_structure():
    """Setup research paper folder structure"""
    
    print("🔥🔥🔥 SETTING UP RESEARCH PAPER STRUCTURE 🔥🔥🔥")
    print("=" * 60)
    
    # Create main research paper directory
    paper_dir = "breakthrough_compression_research_paper"
    
    if os.path.exists(paper_dir):
        print(f"📁 Removing existing directory: {paper_dir}")
        shutil.rmtree(paper_dir)
    
    os.makedirs(paper_dir, exist_ok=True)
    print(f"📁 Created main directory: {paper_dir}")
    
    # Create subdirectories
    subdirs = [
        "paper",           # Main research paper
        "data",           # Experimental data and results
        "code",           # Algorithm implementations
        "figures",        # Charts, graphs, visualizations
        "appendices",     # Additional technical details
        "submission"      # Ready-to-submit versions
    ]
    
    for subdir in subdirs:
        subdir_path = os.path.join(paper_dir, subdir)
        os.makedirs(subdir_path, exist_ok=True)
        print(f"   📂 Created: {subdir}/")
    
    # Copy relevant files
    print(f"\n📋 COPYING RESEARCH FILES:")
    
    # Copy algorithm implementations
    algorithm_files = [
        "real_1gb_to_8kb_compression.py",
        "mistral_7b_file_compression.py",
        "breakthrough_recursive_compressor.py",
        "real_breakthrough_implementation.py"
    ]
    
    for file in algorithm_files:
        if os.path.exists(file):
            dest = os.path.join(paper_dir, "code", file)
            shutil.copy2(file, dest)
            print(f"   ✅ Copied: {file} → code/")
    
    # Copy result files
    result_files = [
        "real_1gb_compression_results_20250617_123456.json",
        "large_scale_test_results_20250617_122823.json",
        "breakthrough_algorithm_results_20250617_123456.json"
    ]
    
    for pattern in ["*compression_results*.json", "*test_results*.json"]:
        import glob
        for file in glob.glob(pattern):
            dest = os.path.join(paper_dir, "data", os.path.basename(file))
            shutil.copy2(file, dest)
            print(f"   ✅ Copied: {file} → data/")
    
    # Copy compressed model results
    if os.path.exists("mistral_7b_compressed"):
        dest_dir = os.path.join(paper_dir, "data", "mistral_7b_compressed")
        shutil.copytree("mistral_7b_compressed", dest_dir)
        print(f"   ✅ Copied: mistral_7b_compressed/ → data/")
    
    # Copy compressed files
    compressed_files = ["compressed_8kb_output.dat"]
    for file in compressed_files:
        if os.path.exists(file):
            dest = os.path.join(paper_dir, "data", file)
            shutil.copy2(file, dest)
            print(f"   ✅ Copied: {file} → data/")
    
    print(f"\n✅ Research paper structure created successfully!")
    print(f"📁 Main directory: {paper_dir}/")
    print(f"📄 Ready for paper creation")
    
    return paper_dir

if __name__ == "__main__":
    setup_research_paper_structure()
