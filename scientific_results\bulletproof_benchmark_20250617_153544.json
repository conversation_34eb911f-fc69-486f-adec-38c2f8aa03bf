{"random_10mb": {"dataset_name": "random_10mb", "description": "Cryptographically random data (incompressible baseline)", "original_size": 10485760, "data_hash": "cb4576dc9f26ad10371a491ef89a0301336f88ee8884e0a2f544ecaef463aa97", "timestamp": "2025-06-17T15:35:27.483137", "compression_results": {"breakthrough": {"compressed_size": 644, "compression_ratio": 13347.456968331455, "processing_time": 0.0031876564025878906, "amplification_factor": 0.8197557723622758, "entropy": 0.9997715901229902, "pattern_score": 0.0036600732014640293, "compressibility": 0.30918551941130706, "method": "breakthrough_recursive"}, "gzip_max": {"compressed_size": 10488978, "compression_ratio": 0.9996932017590274, "processing_time": 0.28969502449035645, "method": "gzip_level_9"}, "bzip2_max": {"compressed_size": 10533283, "compression_ratio": 0.9954883012257432, "processing_time": 1.161362886428833, "method": "bzip2_level_9"}, "lzma_max": {"compressed_size": 10486344, "compression_ratio": 0.9999443085216354, "processing_time": 2.886577606201172, "method": "lzma_level_9"}}}, "repetitive_5mb": {"dataset_name": "repetitive_5mb", "description": "Highly repetitive pattern (maximum compressibility)", "original_size": 5242875, "data_hash": "53dbd3a9e036de6816a89b1770acedab30b707ad72f138e33629b60587a3e152", "timestamp": "2025-06-17T15:35:31.853061", "compression_results": {"breakthrough": {"compressed_size": 636, "compression_ratio": 79348.57028279395, "processing_time": 0.011242151260375977, "amplification_factor": 9.625575795695482, "entropy": 0.4798626294432631, "pattern_score": 0.04878097561951239, "compressibility": 0.3569994915445161, "method": "breakthrough_recursive"}, "gzip_max": {"compressed_size": 15326, "compression_ratio": 342.0902388098656, "processing_time": 0.029207706451416016, "method": "gzip_level_9"}, "bzip2_max": {"compressed_size": 1010, "compression_ratio": 5190.965346534654, "processing_time": 1.4341528415679932, "method": "bzip2_level_9"}, "lzma_max": {"compressed_size": 932, "compression_ratio": 5625.402360515021, "processing_time": 0.13447117805480957, "method": "lzma_level_9"}}}, "text_natural": {"dataset_name": "text_natural", "description": "Natural language text with typical statistical properties", "original_size": 4760000, "data_hash": "6cc4dd742ef2b192d6736a3535a34f62e9bde5a43e4a4031074bd43179deffcf", "timestamp": "2025-06-17T15:35:33.479412", "compression_results": {"breakthrough": {"compressed_size": 634, "compression_ratio": 77840.46127583008, "processing_time": 0.019846439361572266, "amplification_factor": 10.367826144721906, "entropy": 0.5144631169786958, "pattern_score": 0.1009420188403768, "compressibility": 0.3779154356709651, "method": "breakthrough_recursive"}, "gzip_max": {"compressed_size": 21099, "compression_ratio": 225.60310915209251, "processing_time": 0.029681921005249023, "method": "gzip_level_9"}, "bzip2_max": {"compressed_size": 5323, "compression_ratio": 894.2325756152545, "processing_time": 0.9595487117767334, "method": "bzip2_level_9"}, "lzma_max": {"compressed_size": 1124, "compression_ratio": 4234.875444839858, "processing_time": 0.09124875068664551, "method": "lzma_level_9"}}}, "neural_weights": {"dataset_name": "neural_weights", "description": "Simulated neural network weights (float32, normal distribution)", "original_size": 8388608, "data_hash": "3d604b647db35222c3622b19c434afea9de045ec25d7a072cbe4d0a25987fc17", "timestamp": "2025-06-17T15:35:34.598844", "compression_results": {"breakthrough": {"compressed_size": 640, "compression_ratio": 17003.88388571063, "processing_time": 0.008238077163696289, "amplification_factor": 1.297293387276507, "entropy": 0.9194334376113013, "pattern_score": 0.004020080401608032, "compressibility": 0.3095352848539816, "method": "breakthrough_recursive"}, "gzip_max": {"compressed_size": 7780695, "compression_ratio": 1.078130938174546, "processing_time": 0.3748793601989746, "method": "gzip_level_9"}, "bzip2_max": {"compressed_size": 7957188, "compression_ratio": 1.05421764573113, "processing_time": 0.886406421661377, "method": "bzip2_level_9"}, "lzma_max": {"compressed_size": 7727116, "compression_ratio": 1.0856065833617614, "processing_time": 2.3133633136749268, "method": "lzma_level_9"}}}, "structured_json": {"dataset_name": "structured_json", "description": "Structured JSON data with repetitive patterns", "original_size": 9964814, "data_hash": "a0963db088921b054c7d932774d35751758d15f97c95d761deb36bd544cbf2e3", "timestamp": "2025-06-17T15:35:38.203283", "compression_results": {"breakthrough": {"compressed_size": 636, "compression_ratio": 120923.07232439452, "processing_time": 0.012893199920654297, "amplification_factor": 7.717863474251995, "entropy": 0.5782599191493564, "pattern_score": 0.02890057801156023, "compressibility": 0.3439965736691248, "method": "breakthrough_recursive"}, "gzip_max": {"compressed_size": 845624, "compression_ratio": 11.783977275952433, "processing_time": 0.21178436279296875, "method": "gzip_level_9"}, "bzip2_max": {"compressed_size": 483613, "compression_ratio": 20.6049341105388, "processing_time": 0.904611349105835, "method": "bzip2_level_9"}, "lzma_max": {"compressed_size": 276328, "compression_ratio": 36.0615428042037, "processing_time": 4.892172813415527, "method": "lzma_level_9"}}}, "mixed_realistic": {"dataset_name": "mixed_realistic", "description": "Mixed data types simulating realistic file content", "original_size": 2110000, "data_hash": "8a0a307123becef1132098758eda7c854d8b1664d59e9e52d59d778391d0b2f3", "timestamp": "2025-06-17T15:35:44.230661", "compression_results": {"breakthrough": {"compressed_size": 624, "compression_ratio": 12024.814024360809, "processing_time": 0.01454305648803711, "amplification_factor": 3.55615353137495, "entropy": 0.8659028573095361, "pattern_score": 0.09702194043880878, "compressibility": 0.6903487610796359, "method": "breakthrough_recursive"}, "gzip_max": {"compressed_size": 24610, "compression_ratio": 85.73750507923609, "processing_time": 0.01233530044555664, "method": "gzip_level_9"}, "bzip2_max": {"compressed_size": 68861, "compression_ratio": 30.6414370979219, "processing_time": 0.4546823501586914, "method": "bzip2_level_9"}, "lzma_max": {"compressed_size": 11172, "compression_ratio": 188.86501969208737, "processing_time": 0.07266640663146973, "method": "lzma_level_9"}}}}