#!/usr/bin/env python3
"""
📊 DISCOVERY MONITOR
===================

Monitor the extended Gemini discovery session and provide real-time updates.
"""

import os
import json
import time
import glob
from datetime import datetime

class DiscoveryMonitor:
    """
    Monitor and analyze ongoing discovery sessions
    """
    
    def __init__(self):
        self.last_check = datetime.now()
        self.discoveries_found = 0
        
    def monitor_discoveries(self):
        """Monitor discovery files for updates"""
        
        print("📊📊📊 DISCOVERY MONITOR 📊📊📊")
        print("=" * 50)
        print("🔍 Monitoring extended Gemini discovery session...")
        print("⏱️  Checking for new discoveries every 30 seconds")
        print("=" * 50)
        print()
        
        while True:
            try:
                # Find latest discovery files
                discovery_files = glob.glob("extended_discoveries_*.json")
                
                if discovery_files:
                    # Get most recent file
                    latest_file = max(discovery_files, key=os.path.getctime)
                    
                    # Load and analyze
                    with open(latest_file, 'r') as f:
                        data = json.load(f)
                    
                    session_info = data.get('session_info', {})
                    discoveries = data.get('discoveries', [])
                    
                    current_iteration = session_info.get('iteration', 0)
                    tokens_used = session_info.get('tokens_used', 0)
                    
                    # Count successful discoveries
                    successful = len([d for d in discoveries if 'error' not in d])
                    
                    # Find best ratios
                    best_ratio = 0
                    best_algorithm = "None"
                    
                    for discovery in discoveries:
                        if 'theoretical_ratio_numeric' in discovery:
                            ratio = discovery['theoretical_ratio_numeric']
                            if ratio > best_ratio:
                                best_ratio = ratio
                                best_algorithm = discovery.get('algorithm_name', 'Unknown')
                    
                    # Display current status
                    print(f"\r📊 LIVE STATUS: Iteration {current_iteration}/200 | "
                          f"Successful: {successful} | "
                          f"Best: {best_ratio:,.0f}× ({best_algorithm}) | "
                          f"Tokens: {tokens_used:,}", end="", flush=True)
                    
                    # Check for breakthroughs
                    if best_ratio >= 131072 and self.discoveries_found != successful:
                        print(f"\n🚀 BREAKTHROUGH DETECTED!")
                        print(f"   Algorithm: {best_algorithm}")
                        print(f"   Ratio: {best_ratio:,.0f}×")
                        print(f"   Iteration: {current_iteration}")
                        
                    self.discoveries_found = successful
                    
                    # Check if session complete
                    if current_iteration >= 200:
                        print(f"\n✅ DISCOVERY SESSION COMPLETE!")
                        break
                
                else:
                    print(f"\r⏳ Waiting for discovery files...", end="", flush=True)
                
                time.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                print(f"\n🛑 Monitoring stopped by user")
                break
            except Exception as e:
                print(f"\n❌ Monitor error: {e}")
                time.sleep(30)
    
    def analyze_final_results(self):
        """Analyze final discovery results"""
        
        print(f"\n📊 ANALYZING FINAL RESULTS")
        print("=" * 50)
        
        # Find final results file
        final_files = glob.glob("extended_gemini_discoveries_*.json")
        
        if final_files:
            latest_final = max(final_files, key=os.path.getctime)
            
            with open(latest_final, 'r') as f:
                data = json.load(f)
            
            session_info = data['session_info']
            discoveries = data['discoveries']
            
            print(f"📈 SESSION SUMMARY:")
            print(f"   Total iterations: {session_info['iterations_completed']}")
            print(f"   Successful discoveries: {session_info['successful_discoveries']}")
            print(f"   Best ratio: {session_info['best_theoretical_ratio']:,.0f}×")
            print(f"   Total time: {session_info['total_time_seconds']:.0f}s")
            print(f"   Tokens used: {session_info['tokens_used']:,}")
            
            # Analyze algorithm types
            algorithm_types = {}
            breakthrough_algorithms = []
            
            for discovery in discoveries:
                if 'error' not in discovery:
                    prompt_type = discovery.get('prompt_type', 'unknown')
                    algorithm_types[prompt_type] = algorithm_types.get(prompt_type, 0) + 1
                    
                    ratio = discovery.get('theoretical_ratio_numeric', 0)
                    if ratio >= 131072:
                        breakthrough_algorithms.append({
                            'name': discovery.get('algorithm_name', 'Unknown'),
                            'ratio': ratio,
                            'iteration': discovery.get('iteration', 0)
                        })
            
            print(f"\n🧬 ALGORITHM TYPES DISCOVERED:")
            for algo_type, count in algorithm_types.items():
                print(f"   {algo_type}: {count} algorithms")
            
            if breakthrough_algorithms:
                print(f"\n🚀 BREAKTHROUGH ALGORITHMS (≥131,072×):")
                for algo in breakthrough_algorithms:
                    print(f"   {algo['name']}: {algo['ratio']:,.0f}× (Iteration {algo['iteration']})")
            else:
                print(f"\n📊 No breakthrough algorithms found yet")
            
            print(f"\n💾 Final results file: {latest_final}")
        
        else:
            print(f"❌ No final results file found")

def main():
    """Main monitoring execution"""
    
    monitor = DiscoveryMonitor()
    
    try:
        # Monitor ongoing session
        monitor.monitor_discoveries()
        
        # Analyze final results
        monitor.analyze_final_results()
        
    except KeyboardInterrupt:
        print(f"\n🛑 Monitoring stopped")
        
        # Still try to analyze any available results
        monitor.analyze_final_results()

if __name__ == "__main__":
    main()
