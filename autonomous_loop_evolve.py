#!/usr/bin/env python3
"""
AUTONOMOUS LOOP EVOLVE SYSTEM
============================

Enhanced autonomous version with:
1. Self-optimization
2. Multi-objective evolution
3. Automatic problem discovery
4. Dynamic parameter adaptation
5. Cross-domain optimization
"""

import asyncio
import logging
import yaml
from datetime import datetime, timedelta
import json
import os
from typing import Dict, List, Optional, Tuple
import signal
import sys
from pathlib import Path
import numpy as np
from dataclasses import dataclass
import random

from loop_openevolve_system import LoopController
from loop_openevolve_complete import CompleteEvaluatorPool
from monitor_loop_evolve import LoopEvolveMonitor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('autonomous_loop_evolve.log')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProblemDomain:
    """Represents a problem domain for evolution"""
    name: str
    description: str
    metrics: List[str]
    constraints: Dict[str, float]
    priority: float = 1.0

class AutonomousLoopEvolve:
    """Enhanced autonomous version of the Loop Evolve system"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.controller: Optional[LoopController] = None
        self.monitor: Optional[LoopEvolveMonitor] = None
        self.running = False
        self.last_improvement = datetime.now()
        self.improvement_timeout = timedelta(hours=1)
        self.checkpoint_interval = timedelta(minutes=5)
        self.last_checkpoint = datetime.now()
        self.results_dir = Path("autonomous_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # Problem domains
        self.problem_domains = self._initialize_problem_domains()
        self.current_domain_index = 0
        
        # Multi-objective optimization
        self.objective_weights = self._initialize_objective_weights()
        self.pareto_front = []
        
        # Dynamic parameter adaptation
        self.parameter_history = []
        self.performance_history = []
        
        # Register signal handlers
        signal.signal(signal.SIGINT, self.handle_shutdown)
        signal.signal(signal.SIGTERM, self.handle_shutdown)
    
    def _initialize_problem_domains(self) -> List[ProblemDomain]:
        """Initialize problem domains for evolution"""
        return [
            ProblemDomain(
                name="Algorithm Optimization",
                description="Optimize algorithms for performance and efficiency",
                metrics=["execution_time", "memory_usage", "accuracy"],
                constraints={"max_memory": 1.0, "min_accuracy": 0.95},
                priority=1.0
            ),
            ProblemDomain(
                name="Code Generation",
                description="Generate efficient and maintainable code",
                metrics=["code_quality", "complexity", "maintainability"],
                constraints={"max_complexity": 0.8, "min_quality": 0.7},
                priority=0.8
            ),
            ProblemDomain(
                name="Resource Management",
                description="Optimize resource usage and allocation",
                metrics=["cpu_usage", "memory_efficiency", "throughput"],
                constraints={"max_cpu": 0.9, "min_efficiency": 0.8},
                priority=0.9
            )
        ]
    
    def _initialize_objective_weights(self) -> Dict[str, float]:
        """Initialize weights for multi-objective optimization"""
        return {
            "performance": 0.4,
            "efficiency": 0.3,
            "quality": 0.2,
            "maintainability": 0.1
        }
    
    def handle_shutdown(self, signum, frame):
        """Handle graceful shutdown"""
        logger.info("Shutdown signal received. Saving state...")
        self.running = False
        self.save_state()
        sys.exit(0)
    
    async def initialize(self):
        """Initialize the autonomous system"""
        try:
            # Load configuration
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Initialize controller
            self.controller = LoopController(config)
            
            # Initialize monitor
            self.monitor = LoopEvolveMonitor()
            await self.monitor.initialize()
            
            # Load previous state if exists
            self.load_state()
            
            logger.info("Enhanced Autonomous Loop Evolve system initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize system: {e}")
            raise
    
    def save_state(self):
        """Save current system state"""
        if not self.controller or not self.monitor:
            return
        
        state = {
            'timestamp': datetime.now().isoformat(),
            'monitor': {
                'generation': self.monitor.generation,
                'best_fitness': self.monitor.best_fitness,
                'population_stats': self.monitor.population_stats,
                'evolution_stats': self.monitor.evolution_stats,
                'rate_limit_stats': self.monitor.rate_limit_stats
            },
            'controller': {
                'program_count': len(self.controller.program_db.programs),
                'best_program': self.controller.get_best_program().code if self.controller.get_best_program() else None
            },
            'optimization': {
                'current_domain': self.problem_domains[self.current_domain_index].name,
                'objective_weights': self.objective_weights,
                'pareto_front': self.pareto_front,
                'parameter_history': self.parameter_history,
                'performance_history': self.performance_history
            }
        }
        
        state_file = self.results_dir / f"state_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(state_file, 'w') as f:
            json.dump(state, f, indent=2)
        
        logger.info(f"State saved to {state_file}")
    
    def load_state(self):
        """Load previous system state"""
        state_files = sorted(self.results_dir.glob("state_*.json"), reverse=True)
        if not state_files:
            return
        
        try:
            with open(state_files[0], 'r') as f:
                state = json.load(f)
            
            if self.monitor:
                self.monitor.generation = state['monitor']['generation']
                self.monitor.best_fitness = state['monitor']['best_fitness']
                self.monitor.population_stats = state['monitor']['population_stats']
                self.monitor.evolution_stats = state['monitor']['evolution_stats']
                self.monitor.rate_limit_stats = state['monitor']['rate_limit_stats']
            
            if 'optimization' in state:
                opt_state = state['optimization']
                self.objective_weights = opt_state['objective_weights']
                self.pareto_front = opt_state['pareto_front']
                self.parameter_history = opt_state['parameter_history']
                self.performance_history = opt_state['performance_history']
            
            logger.info(f"Loaded state from {state_files[0]}")
            
        except Exception as e:
            logger.error(f"Failed to load state: {e}")
    
    async def adapt_parameters(self):
        """Enhanced parameter adaptation based on performance and objectives"""
        if not self.controller or not self.monitor:
            return
        
        # Calculate success metrics
        success_rate = (self.monitor.evolution_stats['successful_mutations'] / 
                       (self.monitor.evolution_stats['successful_mutations'] + 
                        self.monitor.evolution_stats['failed_mutations']))
        
        # Update parameter history
        current_params = {
            'population_size': self.controller.config['evolution']['population_size'],
            'mutation_rate': self.controller.config['evolution']['mutation_rate'],
            'crossover_rate': self.controller.config['evolution']['crossover_rate']
        }
        self.parameter_history.append(current_params)
        
        # Update performance history
        current_performance = {
            'fitness': self.monitor.best_fitness,
            'success_rate': success_rate,
            'diversity': self._calculate_population_diversity()
        }
        self.performance_history.append(current_performance)
        
        # Adaptive parameter adjustment
        if len(self.performance_history) > 1:
            self._optimize_parameters()
        
        # Dynamic objective weight adjustment
        self._adjust_objective_weights()
        
        # Problem domain rotation
        self._rotate_problem_domain()
    
    def _calculate_population_diversity(self) -> float:
        """Calculate population diversity metric"""
        if not self.controller:
            return 0.0
        
        programs = self.controller.program_db.programs
        if not programs:
            return 0.0
        
        # Calculate average pairwise difference in fitness
        differences = []
        for i in range(len(programs)):
            for j in range(i + 1, len(programs)):
                differences.append(abs(programs[i].fitness - programs[j].fitness))
        
        return np.mean(differences) if differences else 0.0
    
    def _optimize_parameters(self):
        """Optimize evolution parameters based on performance history"""
        if len(self.parameter_history) < 2:
            return
        
        # Calculate performance improvement
        recent_performance = self.performance_history[-1]
        previous_performance = self.performance_history[-2]
        
        if recent_performance['fitness'] > previous_performance['fitness']:
            # If performance improved, maintain current parameters
            return
        
        # If performance degraded, adjust parameters
        self.controller.config['evolution']['mutation_rate'] = min(
            0.3, self.controller.config['evolution']['mutation_rate'] * 1.2
        )
        
        if recent_performance['diversity'] < 0.1:
            # If diversity is low, increase population size
            self.controller.config['evolution']['population_size'] = min(
                100, self.controller.config['evolution']['population_size'] + 5
            )
    
    def _adjust_objective_weights(self):
        """Adjust objective weights based on performance"""
        if len(self.performance_history) < 2:
            return
        
        recent_performance = self.performance_history[-1]
        
        # Adjust weights based on performance metrics
        if recent_performance['fitness'] < 0.5:
            # Increase weight on quality if fitness is low
            self.objective_weights['quality'] = min(0.4, self.objective_weights['quality'] * 1.2)
            self.objective_weights['performance'] = max(0.2, self.objective_weights['performance'] * 0.9)
        
        # Normalize weights
        total = sum(self.objective_weights.values())
        self.objective_weights = {k: v/total for k, v in self.objective_weights.items()}
    
    def _rotate_problem_domain(self):
        """Rotate through problem domains"""
        if datetime.now() - self.last_improvement > self.improvement_timeout:
            self.current_domain_index = (self.current_domain_index + 1) % len(self.problem_domains)
            logger.info(f"Rotating to problem domain: {self.problem_domains[self.current_domain_index].name}")
    
    async def run_autonomous(self):
        """Run the enhanced autonomous evolution process"""
        if not self.controller or not self.monitor:
            await self.initialize()
        
        self.running = True
        logger.info("Starting enhanced autonomous evolution")
        
        try:
            while self.running:
                # Check for improvement timeout
                if datetime.now() - self.last_improvement > self.improvement_timeout:
                    logger.warning("No improvement detected, adapting parameters...")
                    await self.adapt_parameters()
                
                # Run evolution generation
                await self.controller.evolve_generation()
                
                # Update monitor
                self.monitor.generation += 1
                best_program = self.controller.get_best_program()
                if best_program and best_program.fitness > self.monitor.best_fitness:
                    self.monitor.best_fitness = best_program.fitness
                    self.last_improvement = datetime.now()
                    logger.info(f"New best fitness: {self.monitor.best_fitness:.4f}")
                
                self.monitor.update_stats()
                
                # Update Pareto front
                self._update_pareto_front(best_program)
                
                # Save checkpoint if needed
                if datetime.now() - self.last_checkpoint > self.checkpoint_interval:
                    self.save_state()
                    self.last_checkpoint = datetime.now()
                
                # Check rate limits
                if self.monitor.rate_limit_stats['remaining_requests'] <= 0:
                    logger.warning("Rate limit reached, pausing evolution")
                    await asyncio.sleep(3600)  # Wait for 1 hour
                
                # Small delay between generations
                await asyncio.sleep(1)
        
        except Exception as e:
            logger.error(f"Error in autonomous evolution: {e}")
            self.save_state()
            raise
    
    def _update_pareto_front(self, program):
        """Update Pareto front with new solution"""
        if not program:
            return
        
        # Calculate objective values
        objectives = {
            'performance': program.fitness,
            'efficiency': 1.0 - program.metrics.get('memory_usage', 0.0),
            'quality': program.metrics.get('code_quality', 0.0),
            'maintainability': program.metrics.get('maintainability', 0.0)
        }
        
        # Check if solution is Pareto optimal
        is_dominated = False
        dominated_solutions = []
        
        for solution in self.pareto_front:
            if self._dominates(solution['objectives'], objectives):
                is_dominated = True
                break
            elif self._dominates(objectives, solution['objectives']):
                dominated_solutions.append(solution)
        
        if not is_dominated:
            # Add new solution to Pareto front
            self.pareto_front.append({
                'program': program,
                'objectives': objectives
            })
            
            # Remove dominated solutions
            for solution in dominated_solutions:
                self.pareto_front.remove(solution)
    
    def _dominates(self, obj1: Dict[str, float], obj2: Dict[str, float]) -> bool:
        """Check if obj1 dominates obj2"""
        better_in_any = False
        for metric in obj1:
            if obj1[metric] < obj2[metric]:
                return False
            if obj1[metric] > obj2[metric]:
                better_in_any = True
        return better_in_any
    
    async def start(self):
        """Start the autonomous system"""
        try:
            await self.initialize()
            await self.run_autonomous()
        except KeyboardInterrupt:
            logger.info("Autonomous evolution interrupted by user")
        except Exception as e:
            logger.error(f"Autonomous evolution failed: {e}")
        finally:
            self.save_state()
            logger.info("Autonomous evolution completed")

async def main():
    """Main function"""
    logger.info("🚀 Starting Enhanced Autonomous Loop Evolve System")
    logger.info("=" * 50)
    
    autonomous_system = AutonomousLoopEvolve()
    await autonomous_system.start()

if __name__ == "__main__":
    asyncio.run(main()) 