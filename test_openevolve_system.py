#!/usr/bin/env python3
"""
TEST OPENEVOLVE SYSTEM
=====================

Test script to verify the OpenEvolve system with Gemini 2.5 Flash model
and proper rate limiting configuration.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List
import yaml

from loop_openevolve_system import <PERSON><PERSON><PERSON>roller
from loop_openevolve_complete import CompleteEvaluatorPool

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('openevolve_test.log')
    ]
)
logger = logging.getLogger(__name__)

class RateLimiter:
    """Rate limiter for Gemini API calls"""
    
    def __init__(self, requests_per_day: int = 25, tokens_per_request: int = 250000):
        self.requests_per_day = requests_per_day
        self.tokens_per_request = tokens_per_request
        self.requests_today = 0
        self.last_reset = datetime.now()
        self.request_timestamps: List[datetime] = []
    
    def can_make_request(self) -> bool:
        """Check if a new request can be made"""
        self._reset_if_new_day()
        
        # Check daily limit
        if self.requests_today >= self.requests_per_day:
            logger.warning("Daily request limit reached")
            return False
        
        # Check token limit
        current_tokens = sum(1 for ts in self.request_timestamps 
                           if datetime.now() - ts < timedelta(days=1))
        if current_tokens >= self.tokens_per_request:
            logger.warning("Token limit reached")
            return False
        
        return True
    
    def record_request(self):
        """Record a new request"""
        self._reset_if_new_day()
        self.requests_today += 1
        self.request_timestamps.append(datetime.now())
    
    def _reset_if_new_day(self):
        """Reset counters if it's a new day"""
        if datetime.now().date() > self.last_reset.date():
            self.requests_today = 0
            self.request_timestamps = []
            self.last_reset = datetime.now()
    
    def get_remaining_requests(self) -> int:
        """Get remaining requests for today"""
        self._reset_if_new_day()
        return self.requests_per_day - self.requests_today
    
    def get_remaining_tokens(self) -> int:
        """Get remaining tokens for today"""
        self._reset_if_new_day()
        current_tokens = sum(1 for ts in self.request_timestamps 
                           if datetime.now() - ts < timedelta(days=1))
        return self.tokens_per_request - current_tokens

async def test_llm_generation(controller: LoopController, rate_limiter: RateLimiter):
    """Test LLM code generation with rate limiting"""
    logger.info("Testing LLM code generation...")
    
    test_prompt = """
    Create a simple sorting algorithm that:
    1. Uses quicksort for large arrays
    2. Uses insertion sort for small arrays
    3. Includes an evaluate() function that returns performance metrics
    """
    
    try:
        if not rate_limiter.can_make_request():
            logger.error("Rate limit reached, cannot test generation")
            return
        
        code = await controller.llm_ensemble.generate_code(
            test_prompt,
            provider="gemini",
            temperature=0.7
        )
        
        rate_limiter.record_request()
        
        logger.info("Generated code:")
        logger.info("-" * 40)
        logger.info(code)
        logger.info("-" * 40)
        
    except Exception as e:
        logger.error(f"Generation failed: {e}")

async def test_program_evaluation(controller: LoopController):
    """Test program evaluation"""
    logger.info("Testing program evaluation...")
    
    test_program = """
def sort_array(arr):
    if len(arr) <= 1:
        return arr
    
    if len(arr) < 10:
        return insertion_sort(arr)
    else:
        return quick_sort(arr)

def insertion_sort(arr):
    for i in range(1, len(arr)):
        key = arr[i]
        j = i - 1
        while j >= 0 and arr[j] > key:
            arr[j + 1] = arr[j]
            j -= 1
        arr[j + 1] = key
    return arr

def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quick_sort(left) + middle + quick_sort(right)

def evaluate():
    import random
    import time
    
    # Test cases
    test_sizes = [10, 100, 1000]
    results = {}
    
    for size in test_sizes:
        # Generate random array
        arr = [random.randint(0, 1000) for _ in range(size)]
        
        # Measure time
        start = time.time()
        sorted_arr = sort_array(arr)
        end = time.time()
        
        # Verify correctness
        is_sorted = all(sorted_arr[i] <= sorted_arr[i+1] for i in range(len(sorted_arr)-1))
        
        results[f'size_{size}'] = {
            'time': end - start,
            'correct': is_sorted
        }
    
    # Calculate overall fitness
    fitness = sum(1 for r in results.values() if r['correct']) / len(results)
    
    return {
        'fitness': fitness,
        'metrics': results
    }
"""
    
    try:
        metrics = await controller.evaluator_pool.evaluate_program(
            controller.program_db.programs[0] if controller.program_db.programs else None
        )
        
        logger.info("Evaluation results:")
        logger.info("-" * 40)
        logger.info(f"Fitness: {metrics.get('fitness', 0.0):.3f}")
        logger.info("Metrics:")
        for key, value in metrics.get('metrics', {}).items():
            logger.info(f"  {key}: {value}")
        logger.info("-" * 40)
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")

async def test_evolution_cycle(controller: LoopController, rate_limiter: RateLimiter):
    """Test a single evolution cycle"""
    logger.info("Testing evolution cycle...")
    
    try:
        # Check rate limits
        if not rate_limiter.can_make_request():
            logger.error("Rate limit reached, cannot run evolution cycle")
            return
        
        # Run one generation
        await controller.evolve_generation()
        
        # Get best program
        best_program = controller.get_best_program()
        if best_program:
            logger.info(f"Best program fitness: {best_program.fitness:.3f}")
            logger.info("Best program code:")
            logger.info("-" * 40)
            logger.info(best_program.code)
            logger.info("-" * 40)
        
    except Exception as e:
        logger.error(f"Evolution cycle failed: {e}")

async def main():
    """Main test function"""
    logger.info("🚀 Starting OpenEvolve System Test")
    logger.info("=" * 50)
    
    # Load configuration
    with open("config.yaml", "r") as f:
        config = yaml.safe_load(f)
    
    # Update Gemini model
    config["llm"]["providers"]["gemini"]["model"] = "gemini-2.5-flash"
    
    # Initialize rate limiter
    rate_limiter = RateLimiter(
        requests_per_day=25,
        tokens_per_request=250000
    )
    
    # Initialize controller
    controller = LoopController(config)
    
    # Run tests
    logger.info("\n1. Testing LLM Generation")
    await test_llm_generation(controller, rate_limiter)
    
    logger.info("\n2. Testing Program Evaluation")
    await test_program_evaluation(controller)
    
    logger.info("\n3. Testing Evolution Cycle")
    await test_evolution_cycle(controller, rate_limiter)
    
    # Print rate limit status
    logger.info("\nRate Limit Status:")
    logger.info(f"Remaining requests today: {rate_limiter.get_remaining_requests()}")
    logger.info(f"Remaining tokens today: {rate_limiter.get_remaining_tokens()}")
    
    logger.info("\n✅ Test completed")

if __name__ == "__main__":
    asyncio.run(main()) 