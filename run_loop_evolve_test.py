#!/usr/bin/env python3
"""
LOOP EVOLVE TEST RUN
===================

Run a test evolution cycle with the Loop Evolve system
to demonstrate its operation and capabilities.
"""

import asyncio
import logging
import yaml
from datetime import datetime
from typing import Dict, List
import json

from loop_openevolve_system import <PERSON><PERSON><PERSON><PERSON><PERSON>
from loop_openevolve_complete import CompleteEvaluatorPool
from monitor_loop_evolve import LoopEvolveMonitor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('loop_evolve_test.log')
    ]
)
logger = logging.getLogger(__name__)

async def run_test_evolution():
    """Run a test evolution cycle"""
    # Load configuration
    with open("config.yaml", "r") as f:
        config = yaml.safe_load(f)
    
    # Initialize controller
    controller = LoopController(config)
    
    # Initialize monitor
    monitor = LoopEvolveMonitor()
    await monitor.initialize()
    
    # Test prompt for algorithm discovery
    test_prompt = """
    Create an algorithm that:
    1. Implements a hybrid sorting approach
    2. Uses quicksort for large arrays
    3. Uses insertion sort for small arrays
    4. Includes adaptive pivot selection
    5. Has an evaluate() function that returns:
       - Sorting time
       - Memory usage
       - Correctness verification
       - Performance metrics
    """
    
    # Run evolution
    logger.info("Starting test evolution cycle...")
    
    try:
        # Run 5 generations
        for gen in range(5):
            logger.info(f"\nGeneration {gen + 1}/5")
            
            # Evolve generation
            await controller.evolve_generation()
            
            # Get best program
            best_program = controller.get_best_program()
            if best_program:
                logger.info(f"Best fitness: {best_program.fitness:.4f}")
                logger.info("Best program code:")
                logger.info("-" * 40)
                logger.info(best_program.code)
                logger.info("-" * 40)
            
            # Update monitor
            monitor.generation = gen + 1
            if best_program:
                monitor.best_fitness = best_program.fitness
            monitor.update_stats()
            
            # Save checkpoint
            monitor.save_checkpoint()
            
            # Small delay between generations
            await asyncio.sleep(1)
        
        # Save final results
        results = {
            'timestamp': datetime.now().isoformat(),
            'generations_completed': 5,
            'best_fitness': monitor.best_fitness,
            'population_stats': monitor.population_stats,
            'evolution_stats': monitor.evolution_stats,
            'rate_limit_stats': monitor.rate_limit_stats
        }
        
        with open('test_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info("\nTest evolution cycle completed successfully!")
        logger.info(f"Results saved to test_results.json")
        
    except Exception as e:
        logger.error(f"Error during test evolution: {e}")
        raise

async def main():
    """Main function"""
    logger.info("🚀 Starting Loop Evolve Test Run")
    logger.info("=" * 50)
    
    try:
        await run_test_evolution()
    except KeyboardInterrupt:
        logger.info("\nTest run interrupted by user")
    except Exception as e:
        logger.error(f"Test run failed: {e}")
    finally:
        logger.info("\nTest run completed")

if __name__ == "__main__":
    asyncio.run(main()) 