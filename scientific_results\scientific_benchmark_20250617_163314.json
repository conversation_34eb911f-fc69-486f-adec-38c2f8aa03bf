{"random_1mb": {"dataset_name": "random_1mb", "description": "Pseudo-random data (incompressible baseline)", "original_size": 1048576, "data_hash": "fbbab289f7f94b25736c58be46a994c441fd02552cc6022352e3d86d2fab7c83", "timestamp": "2025-06-17T15:58:57.876285", "compression_results": {"breakthrough": {"compressed_size": 133284, "compression_ratio": 7.867230875423907, "processing_time": 517.6271476745605, "method": "true_breakthrough_compression", "breakthrough_achieved": false}, "gzip_9": {"compressed_size": 4408, "compression_ratio": 237.8802177858439, "processing_time": 0.004603862762451172, "method": "gzip_level_9"}, "bzip2_9": {"compressed_size": 2017, "compression_ratio": 519.8691125433812, "processing_time": 0.28202033042907715, "method": "bzip2_level_9"}, "lzma_9": {"compressed_size": 512, "compression_ratio": 2048.0, "processing_time": 0.0427403450012207, "method": "lzma_level_9"}}}, "repetitive_1mb": {"dataset_name": "repetitive_1mb", "description": "Highly repetitive pattern data", "original_size": 1048553, "data_hash": "500e7f8ad44878209006a19c2fc66fba9e0cc3cc6d2bf52ac5be510aa85321f3", "timestamp": "2025-06-17T16:07:35.847923", "compression_results": {"breakthrough": {"compressed_size": 115800, "compression_ratio": 9.05486183074266, "processing_time": 534.2836458683014, "method": "true_breakthrough_compression", "breakthrough_achieved": false}, "gzip_9": {"compressed_size": 2604, "compression_ratio": 402.6701228878648, "processing_time": 0.0, "method": "gzip_level_9"}, "bzip2_9": {"compressed_size": 252, "compression_ratio": 4160.924603174603, "processing_time": 0.3250577449798584, "method": "bzip2_level_9"}, "lzma_9": {"compressed_size": 312, "compression_ratio": 3360.746794871795, "processing_time": 0.05136299133300781, "method": "lzma_level_9"}}}, "text_natural": {"dataset_name": "text_natural", "description": "Natural language text", "original_size": 880000, "data_hash": "b40322b31386493f40f72ef3bb69cfcc5882bb142011f58b1c69199056e55a3e", "timestamp": "2025-06-17T16:16:30.519153", "compression_results": {"breakthrough": {"compressed_size": 134676, "compression_ratio": 6.534200599958418, "processing_time": 481.32628297805786, "method": "true_breakthrough_compression", "breakthrough_achieved": false}, "gzip_9": {"compressed_size": 2634, "compression_ratio": 334.0926347760061, "processing_time": 0.008008956909179688, "method": "gzip_level_9"}, "bzip2_9": {"compressed_size": 187, "compression_ratio": 4705.882352941177, "processing_time": 0.2314751148223877, "method": "bzip2_level_9"}, "lzma_9": {"compressed_size": 304, "compression_ratio": 2894.7368421052633, "processing_time": 0.05265522003173828, "method": "lzma_level_9"}}}, "binary_float32": {"dataset_name": "binary_float32", "description": "Binary float32 data", "original_size": 1048576, "data_hash": "7d85932f4b6428d2b6138031a631041487abda54b380d48f30b1bb192d588cc1", "timestamp": "2025-06-17T16:24:32.151910", "compression_results": {"gzip_9": {"compressed_size": 494596, "compression_ratio": 2.1200656697587528, "processing_time": 0.05158281326293945, "method": "gzip_level_9"}, "bzip2_9": {"compressed_size": 318460, "compression_ratio": 3.2926458581925515, "processing_time": 0.11138653755187988, "method": "bzip2_level_9"}, "lzma_9": {"compressed_size": 54684, "compression_ratio": 19.175188354911857, "processing_time": 0.3086082935333252, "method": "lzma_level_9"}}}, "enwik8_1mb": {"dataset_name": "enwik8_1mb", "description": "Wikipedia text (enwik8 sample)", "original_size": 1048576, "data_hash": "4fb5efa9f35df431737731bf3c8f38a467b69731940ff82a4ee0e218aae58834", "timestamp": "2025-06-17T16:29:43.157118", "compression_results": {"gzip_9": {"compressed_size": 371078, "compression_ratio": 2.825756309994125, "processing_time": 0.10053682327270508, "method": "gzip_level_9"}, "bzip2_9": {"compressed_size": 294143, "compression_ratio": 3.5648511098343323, "processing_time": 0.11756491661071777, "method": "bzip2_level_9"}, "lzma_9": {"compressed_size": 302764, "compression_ratio": 3.463344387047337, "processing_time": 0.5475306510925293, "method": "lzma_level_9"}}}}