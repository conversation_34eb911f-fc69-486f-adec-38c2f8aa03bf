# Loop OpenEvolve Configuration

# LLM Configuration
llm:
  default_provider: "gemini"  # Primary LLM provider
  providers:
    gemini:
      model: "gemini-2.5-flash"
      temperature: 0.7
      max_tokens: 250000
      rate_limit:
        requests_per_day: 25
        tokens_per_request: 250000
        cooldown_period: 3600  # 1 hour cooldown between batches
    openai:
      model: "gpt-4"
      temperature: 0.7
      max_tokens: 4096

# Evolution Parameters
evolution:
  population_size: 50
  generations: 100
  selection_rate: 0.2
  mutation_rate: 0.1
  crossover_rate: 0.8
  elite_ratio: 0.1
  early_stopping:
    patience: 10
    min_improvement: 0.01

# Evaluation Settings
evaluation:
  timeout: 30
  memory_limit: "1GB"
  worker_count: 4
  safety_checks: true
  metrics:
    - accuracy
    - performance
    - memory_usage
    - code_quality

# Prompt Templates
prompts:
  initial:
    template: |
      Create a new algorithm that:
      1. {requirements}
      2. Must include an evaluate() function
      3. Must be optimized for {optimization_target}
      
      Best programs so far:
      {best_programs}
  
  evolution:
    template: |
      Improve this algorithm:
      {current_code}
      
      Requirements:
      1. {requirements}
      2. Must maintain or improve performance
      3. Must include proper error handling
      
      Best programs so far:
      {best_programs}
  
  refinement:
    template: |
      Refine this algorithm:
      {current_code}
      
      Focus on:
      1. Performance optimization
      2. Code clarity
      3. Error handling
      4. Documentation

# Loop System Settings
loop_system:
  memory_limit: "2GB"
  target_accuracy: 0.95
  checkpoint_interval: 5
  max_evolution_time: 3600  # 1 hour

# Algorithm Discovery Settings
algorithm_discovery:
  target_compression_ratio: 0.5
  accuracy_loss_limit: 0.05
  sparsity_levels: [0.1, 0.3, 0.5, 0.7, 0.9]
  block_sizes: [8, 16, 32, 64]
  optimization_targets:
    - performance
    - memory_usage
    - accuracy
    - sparsity

# Safety Settings
safety:
  memory_usage_limit: "1GB"
  execution_time_limit: 30
  safety_checks: true
  quarantine_policy:
    max_failed_attempts: 3
    quarantine_duration: 3600  # 1 hour

# Logging Settings
logging:
  level: "INFO"
  file: "openevolve.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5
