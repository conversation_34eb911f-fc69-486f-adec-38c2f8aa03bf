#!/usr/bin/env python3
"""
LOOP OPENEVOLVE COMPLETE IMPLEMENTATION
======================================

Complete implementation of the OpenEvolve system including:
- Distributed program evaluation
- Asynchronous evolution pipeline
- Multi-objective optimization
- Checkpointing and recovery
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import numpy as np
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import yaml

from loop_openevolve_system import (
    Program, ProgramDatabase, PromptSampler, LLMEnsemble, LoopConfig
)

logger = logging.getLogger(__name__)

class CompleteEvaluatorPool:
    """Distributed program evaluation pool"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_workers = config.get("evaluation", {}).get("max_workers", 4)
        self.timeout = config.get("evaluation", {}).get("timeout_seconds", 30)
        self.max_memory = config.get("evaluation", {}).get("max_memory_mb", 512)
        
        # Initialize process pool for CPU-bound tasks
        self.process_pool = ProcessPoolExecutor(max_workers=self.max_workers)
        
        # Initialize thread pool for I/O-bound tasks
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers * 2)
        
        # Evaluation cache
        self.evaluation_cache: Dict[str, Dict[str, float]] = {}
        
    async def evaluate_program(self, program: Program) -> Dict[str, float]:
        """Evaluate a program using the distributed pool"""
        
        # Check cache first
        cache_key = str(hash(program.code))
        if cache_key in self.evaluation_cache:
            return self.evaluation_cache[cache_key]
        
        try:
            # Run evaluation in process pool
            loop = asyncio.get_event_loop()
            metrics = await loop.run_in_executor(
                self.process_pool,
                self._evaluate_program_sync,
                program.code
            )
            
            # Cache results
            self.evaluation_cache[cache_key] = metrics
            return metrics
            
        except Exception as e:
            logger.error(f"Evaluation failed: {e}")
            return {"error": 1.0, "fitness": 0.0}
    
    def _evaluate_program_sync(self, code: str) -> Dict[str, float]:
        """Synchronous program evaluation"""
        try:
            # Create isolated namespace
            namespace = {}
            
            # Execute program with timeout
            exec(code, namespace)
            
            # Get evaluation function
            evaluate = namespace.get("evaluate")
            if not evaluate:
                raise ValueError("Program must define evaluate() function")
            
            # Run evaluation
            metrics = evaluate()
            
            # Validate metrics
            if not isinstance(metrics, dict):
                raise ValueError("evaluate() must return a dictionary of metrics")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Program execution failed: {e}")
            return {"error": 1.0, "fitness": 0.0}
    
    async def evaluate_population(self, programs: List[Program]) -> List[Dict[str, float]]:
        """Evaluate a population of programs in parallel"""
        tasks = [self.evaluate_program(program) for program in programs]
        return await asyncio.gather(*tasks)
    
    def shutdown(self):
        """Shutdown evaluation pools"""
        self.process_pool.shutdown()
        self.thread_pool.shutdown()

class LoopController:
    """Main controller for the OpenEvolve system"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config = LoopConfig(config_path)
        self.program_db = ProgramDatabase(self.config.config)
        self.prompt_sampler = PromptSampler(self.config.config)
        self.llm_ensemble = LLMEnsemble(self.config.config)
        self.evaluator_pool = CompleteEvaluatorPool(self.config.config)
        
        # Evolution state
        self.generation = 0
        self.best_fitness = 0.0
        self.checkpoint_dir = Path("checkpoints")
        self.checkpoint_dir.mkdir(exist_ok=True)
    
    async def evolve_generation(self) -> None:
        """Evolve one generation of programs"""
        self.generation += 1
        logger.info(f"Evolving generation {self.generation}")
        
        # Get best programs for context
        best_programs = self.program_db.get_best_programs(n=3)
        
        # Generate new programs
        new_programs = []
        for _ in range(self.config.config["evolution"]["population_size"]):
            # Generate prompt
            prompt = self.prompt_sampler.generate_prompt(
                "evolution",
                {"task_description": "optimize algorithm performance"},
                best_programs
            )
            
            # Generate code
            code = await self.llm_ensemble.generate_code(
                prompt,
                provider=self.config.config["llm"]["default_provider"]
            )
            
            # Create program
            program = Program(
                code=code,
                fitness=0.0,
                metrics={},
                generation=self.generation,
                parent_ids=[p.id for p in best_programs],
                timestamp=time.time()
            )
            
            new_programs.append(program)
        
        # Evaluate new programs
        metrics_list = await self.evaluator_pool.evaluate_population(new_programs)
        
        # Update programs with metrics
        for program, metrics in zip(new_programs, metrics_list):
            program.metrics = metrics
            program.fitness = metrics.get("fitness", 0.0)
            self.program_db.add_program(program)
        
        # Update best fitness
        current_best = max(p.fitness for p in new_programs)
        if current_best > self.best_fitness:
            self.best_fitness = current_best
            logger.info(f"New best fitness: {self.best_fitness:.4f}")
        
        # Create checkpoint
        if self.generation % 10 == 0:
            await self._create_checkpoint()
    
    async def _create_checkpoint(self) -> None:
        """Create a checkpoint of the current state"""
        checkpoint_path = self.checkpoint_dir / f"checkpoint_{self.generation}"
        checkpoint_path.mkdir(exist_ok=True)
        
        # Save programs
        programs_data = [asdict(p) for p in self.program_db.programs]
        with open(checkpoint_path / "programs.json", "w") as f:
            json.dump(programs_data, f)
        
        # Save state
        state = {
            "generation": self.generation,
            "best_fitness": self.best_fitness,
            "timestamp": time.time()
        }
        with open(checkpoint_path / "state.json", "w") as f:
            json.dump(state, f)
        
        logger.info(f"Created checkpoint at generation {self.generation}")
    
    async def load_checkpoint(self, generation: int) -> bool:
        """Load a checkpoint from a specific generation"""
        checkpoint_path = self.checkpoint_dir / f"checkpoint_{generation}"
        if not checkpoint_path.exists():
            return False
        
        try:
            # Load programs
            with open(checkpoint_path / "programs.json", "r") as f:
                programs_data = json.load(f)
            
            self.program_db.programs = [Program(**p) for p in programs_data]
            
            # Load state
            with open(checkpoint_path / "state.json", "r") as f:
                state = json.load(f)
            
            self.generation = state["generation"]
            self.best_fitness = state["best_fitness"]
            
            logger.info(f"Loaded checkpoint from generation {generation}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load checkpoint: {e}")
            return False
    
    async def run_evolution(self, target_generations: int) -> None:
        """Run the complete evolution process"""
        logger.info(f"Starting evolution for {target_generations} generations")
        
        try:
            while self.generation < target_generations:
                await self.evolve_generation()
                
                # Check for early stopping
                if self.best_fitness >= 0.95:  # 95% of maximum possible fitness
                    logger.info("Early stopping: target fitness achieved")
                    break
        
        except KeyboardInterrupt:
            logger.info("Evolution interrupted by user")
        finally:
            # Create final checkpoint
            await self._create_checkpoint()
            self.evaluator_pool.shutdown()
    
    def get_best_program(self) -> Optional[Program]:
        """Get the best program found so far"""
        best_programs = self.program_db.get_best_programs(n=1)
        return best_programs[0] if best_programs else None 