#!/usr/bin/env python3
"""
LOOP OPENEVOLVE SYSTEM
======================

Core implementation of the OpenEvolve architecture for Loop system.
Combines LLM-guided code generation with evolutionary algorithm discovery.
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Any, Optional
from pathlib import Path
import numpy as np
from dataclasses import dataclass, asdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class Program:
    """Represents an evolved program"""
    code: str
    fitness: float
    metrics: Dict[str, float]
    generation: int
    parent_ids: List[int]
    timestamp: float

class ProgramDatabase:
    """Manages the population of evolved programs"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.programs: List[Program] = []
        self.next_id = 0
        self.generation = 0
        
    def add_program(self, program: Program) -> int:
        """Add a new program to the database"""
        program_id = self.next_id
        self.programs.append(program)
        self.next_id += 1
        return program_id
    
    def get_best_programs(self, n: int = 1) -> List[Program]:
        """Get the n best programs by fitness"""
        sorted_programs = sorted(self.programs, key=lambda p: p.fitness, reverse=True)
        return sorted_programs[:n]
    
    def get_programs_by_generation(self, generation: int) -> List[Program]:
        """Get all programs from a specific generation"""
        return [p for p in self.programs if p.generation == generation]

class PromptSampler:
    """Generates prompts for LLM-guided code generation"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.prompt_templates = self._load_prompt_templates()
        
    def _load_prompt_templates(self) -> Dict[str, str]:
        """Load prompt templates from config"""
        return {
            "initial": self.config.get("prompt_templates", {}).get("initial", ""),
            "evolution": self.config.get("prompt_templates", {}).get("evolution", ""),
            "refinement": self.config.get("prompt_templates", {}).get("refinement", "")
        }
    
    def generate_prompt(self, 
                       template_type: str,
                       context: Dict[str, Any],
                       best_programs: List[Program]) -> str:
        """Generate a prompt for the LLM"""
        template = self.prompt_templates.get(template_type, "")
        if not template:
            raise ValueError(f"Unknown prompt template type: {template_type}")
            
        # Format template with context and best programs
        return template.format(
            task_description=context.get("task_description", ""),
            constraints=context.get("constraints", ""),
            best_programs=self._format_best_programs(best_programs)
        )
    
    def _format_best_programs(self, programs: List[Program]) -> str:
        """Format best programs for prompt inclusion"""
        if not programs:
            return "No previous programs available."
            
        formatted = []
        for i, program in enumerate(programs[:3]):  # Include top 3
            formatted.append(f"Program {i+1} (Fitness: {program.fitness:.3f}):")
            formatted.append(program.code)
            formatted.append("")
            
        return "\n".join(formatted)

class LLMEnsemble:
    """Manages multiple LLM providers for code generation"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.providers = self._initialize_providers()
        
    def _initialize_providers(self) -> Dict[str, Any]:
        """Initialize LLM providers from config"""
        providers = {}
        for provider_name, provider_config in self.config.get("llm", {}).get("providers", {}).items():
            if provider_name == "gemini":
                from google.generativeai import GenerativeModel
                providers["gemini"] = GenerativeModel(provider_config["model"])
            elif provider_name == "openai":
                import openai
                openai.api_key = provider_config.get("api_key")
                providers["openai"] = openai
            # Add other providers as needed
        return providers
    
    async def generate_code(self, 
                          prompt: str,
                          provider: str = "gemini",
                          temperature: float = 0.7) -> str:
        """Generate code using specified LLM provider"""
        if provider not in self.providers:
            raise ValueError(f"Unknown LLM provider: {provider}")
            
        if provider == "gemini":
            model = self.providers["gemini"]
            response = await model.generate_content(prompt, temperature=temperature)
            return response.text
        elif provider == "openai":
            response = await self.providers["openai"].ChatCompletion.acreate(
                model=self.config["llm"]["providers"]["openai"]["model"],
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature
            )
            return response.choices[0].message.content
        else:
            raise ValueError(f"Unsupported provider: {provider}")

class LoopConfig:
    """Configuration manager for Loop OpenEvolve system"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "llm": {
                "default_provider": "gemini",
                "providers": {
                    "gemini": {
                        "model": "gemini-2.0-flash-exp",
                        "temperature": 0.7
                    },
                    "openai": {
                        "model": "gpt-4",
                        "temperature": 0.7
                    }
                }
            },
            "evolution": {
                "population_size": 50,
                "generations": 1000,
                "selection_rate": 0.3,
                "mutation_rate": 0.7,
                "crossover_rate": 0.5
            },
            "evaluation": {
                "timeout_seconds": 30,
                "max_memory_mb": 512,
                "safety_checks": True
            },
            "prompt_templates": {
                "initial": "Create a new program that {task_description}",
                "evolution": "Improve this program based on the following best programs:\n{best_programs}",
                "refinement": "Refine this program to better meet these constraints:\n{constraints}"
            }
        } 