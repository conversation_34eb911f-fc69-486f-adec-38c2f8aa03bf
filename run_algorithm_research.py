#!/usr/bin/env python3
"""
🔥 LOOP ALGORITHM RESEARCH RUNNER
================================

Main runner for the Loop algorithm research system.
Evolves and optimizes algorithms using the Loop research framework.
"""

import asyncio
import logging
import json
import time
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional

# Import Loop system components
try:
    from loop_openevolve_complete import Loop<PERSON>ontroller
    from loop_openevolve_system import Program, ProgramDatabase, PromptSampler, LLMEnsemble
except ImportError:
    print("⚠️  Loop system components not found. Using simplified version.")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AlgorithmResearcher:
    """Main algorithm research controller"""
    
    def __init__(self, config_path: str = "algorithm_research_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.generation = 0
        self.best_algorithms = []
        self.research_log = []
        
        # Setup directories
        self.results_dir = Path("algorithm_research/results")
        self.logs_dir = Path("algorithm_research/logs")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
    def _load_config(self) -> Dict[str, Any]:
        """Load research configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "research_focus": "algorithm_optimization",
            "evolution": {
                "population_size": 10,
                "generations": 50,
                "selection_rate": 0.3
            },
            "evaluation": {
                "timeout_seconds": 30,
                "max_memory_mb": 512
            }
        }
    
    def generate_algorithm_prompt(self, algorithm_type: str, focus_area: str) -> str:
        """Generate prompt for algorithm creation"""
        
        template = self.config.get("prompt_templates", {}).get("initial", "")
        
        if not template:
            template = """
Create a novel {algorithm_type} algorithm that {task_description}.

Requirements:
- Must be implementable in Python
- Should be memory efficient  
- Must include proper evaluation metrics
- Focus on {focus_area}

The algorithm should be innovative and efficient.
"""
        
        return template.format(
            algorithm_type=algorithm_type,
            task_description=f"optimizes {focus_area}",
            focus_area=focus_area
        )
    
    def evaluate_algorithm(self, algorithm_code: str) -> Dict[str, float]:
        """Evaluate an algorithm safely"""
        
        try:
            # Create isolated namespace
            namespace = {}
            
            # Execute algorithm code
            exec(algorithm_code, namespace)
            
            # Get evaluation function
            evaluate_func = namespace.get("evaluate")
            if not evaluate_func:
                return {"error": 1.0, "fitness": 0.0}
            
            # Run evaluation with timeout
            start_time = time.time()
            metrics = evaluate_func()
            execution_time = time.time() - start_time
            
            # Validate metrics
            if not isinstance(metrics, dict):
                return {"error": 1.0, "fitness": 0.0}
            
            # Add execution time
            metrics["execution_time"] = execution_time
            
            return metrics
            
        except Exception as e:
            logger.error(f"Algorithm evaluation failed: {e}")
            return {"error": 1.0, "fitness": 0.0, "error_msg": str(e)}
    
    def create_starter_algorithms(self) -> List[str]:
        """Create initial population of algorithms"""
        
        algorithms = []
        
        # Load starter templates
        templates = self.config.get("algorithm_templates", {})
        
        for template_name, template_code in templates.items():
            algorithms.append(template_code)
            logger.info(f"Added starter algorithm: {template_name}")
        
        # Generate additional algorithms if needed
        population_size = self.config.get("evolution", {}).get("population_size", 10)
        
        while len(algorithms) < population_size:
            # Create variations of existing algorithms
            base_algorithm = algorithms[len(algorithms) % len(templates)]
            
            # Simple variation: add comments and minor modifications
            varied_algorithm = f"""
# Variation {len(algorithms)}
{base_algorithm}
"""
            algorithms.append(varied_algorithm)
        
        return algorithms[:population_size]
    
    def run_research_generation(self) -> Dict[str, Any]:
        """Run one generation of algorithm research"""
        
        self.generation += 1
        logger.info(f"Running generation {self.generation}")
        
        # Create or get algorithms
        if self.generation == 1:
            algorithms = self.create_starter_algorithms()
        else:
            algorithms = self.evolve_algorithms()
        
        # Evaluate all algorithms
        generation_results = []
        
        for i, algorithm in enumerate(algorithms):
            logger.info(f"Evaluating algorithm {i+1}/{len(algorithms)}")
            
            metrics = self.evaluate_algorithm(algorithm)
            
            result = {
                "algorithm_id": f"gen_{self.generation}_alg_{i}",
                "generation": self.generation,
                "algorithm_code": algorithm,
                "metrics": metrics,
                "timestamp": time.time()
            }
            
            generation_results.append(result)
        
        # Sort by fitness
        generation_results.sort(key=lambda x: x["metrics"].get("fitness", 0), reverse=True)
        
        # Update best algorithms
        best_this_gen = generation_results[0]
        self.best_algorithms.append(best_this_gen)
        
        # Log generation summary
        best_fitness = best_this_gen["metrics"].get("fitness", 0)
        avg_fitness = sum(r["metrics"].get("fitness", 0) for r in generation_results) / len(generation_results)
        
        generation_summary = {
            "generation": self.generation,
            "best_fitness": best_fitness,
            "average_fitness": avg_fitness,
            "algorithms_evaluated": len(generation_results),
            "timestamp": time.time()
        }
        
        self.research_log.append(generation_summary)
        
        logger.info(f"Generation {self.generation} complete:")
        logger.info(f"  Best fitness: {best_fitness:.4f}")
        logger.info(f"  Average fitness: {avg_fitness:.4f}")
        
        # Save results
        self._save_generation_results(generation_results, generation_summary)
        
        return generation_summary
    
    def evolve_algorithms(self) -> List[str]:
        """Evolve algorithms based on previous generation"""
        
        # For now, return variations of best algorithms
        # In full implementation, this would use LLM to evolve code
        
        if not self.best_algorithms:
            return self.create_starter_algorithms()
        
        population_size = self.config.get("evolution", {}).get("population_size", 10)
        new_algorithms = []
        
        # Keep best algorithms (elitism)
        elite_count = max(1, int(population_size * 0.2))
        for i in range(elite_count):
            if i < len(self.best_algorithms):
                new_algorithms.append(self.best_algorithms[-(i+1)]["algorithm_code"])
        
        # Generate variations
        while len(new_algorithms) < population_size:
            base_algorithm = self.best_algorithms[-1]["algorithm_code"]
            
            # Simple mutation: add variation comment
            variation = f"""
# Evolved variation - Generation {self.generation}
{base_algorithm}
"""
            new_algorithms.append(variation)
        
        return new_algorithms
    
    def _save_generation_results(self, results: List[Dict], summary: Dict):
        """Save generation results to files"""
        
        # Save detailed results
        results_file = self.results_dir / f"generation_{self.generation}_results.json"
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2)
        
        # Save summary
        summary_file = self.results_dir / f"generation_{self.generation}_summary.json"
        with open(summary_file, "w") as f:
            json.dump(summary, f, indent=2)
        
        # Update research log
        log_file = self.logs_dir / "research_log.json"
        with open(log_file, "w") as f:
            json.dump(self.research_log, f, indent=2)
        
        # Save best algorithm
        if self.best_algorithms:
            best_file = self.results_dir / "best_algorithm.py"
            with open(best_file, "w") as f:
                f.write(self.best_algorithms[-1]["algorithm_code"])
    
    def run_research_campaign(self, target_generations: int = 10):
        """Run complete research campaign"""
        
        print(f"🔥 STARTING ALGORITHM RESEARCH CAMPAIGN")
        print(f"=" * 50)
        print(f"Target generations: {target_generations}")
        print(f"Population size: {self.config.get('evolution', {}).get('population_size', 10)}")
        print(f"Focus: {self.config.get('research_focus', 'algorithm_optimization')}")
        
        start_time = time.time()
        
        try:
            for gen in range(target_generations):
                summary = self.run_research_generation()
                
                # Check for early stopping
                if summary["best_fitness"] >= 0.95:
                    logger.info("Early stopping: target fitness achieved")
                    break
                
                # Progress update
                if gen % 5 == 0:
                    elapsed = time.time() - start_time
                    print(f"Progress: {gen+1}/{target_generations} generations, "
                          f"Best fitness: {summary['best_fitness']:.4f}, "
                          f"Time: {elapsed:.1f}s")
        
        except KeyboardInterrupt:
            logger.info("Research interrupted by user")
        
        finally:
            self._generate_final_report()
    
    def _generate_final_report(self):
        """Generate final research report"""
        
        if not self.research_log:
            return
        
        final_report = {
            "campaign_summary": {
                "total_generations": len(self.research_log),
                "best_fitness_achieved": max(log["best_fitness"] for log in self.research_log),
                "final_fitness": self.research_log[-1]["best_fitness"],
                "total_algorithms_evaluated": sum(log["algorithms_evaluated"] for log in self.research_log)
            },
            "research_log": self.research_log,
            "best_algorithms": self.best_algorithms[-5:] if len(self.best_algorithms) > 5 else self.best_algorithms
        }
        
        report_file = self.results_dir / "final_research_report.json"
        with open(report_file, "w") as f:
            json.dump(final_report, f, indent=2)
        
        print(f"\n🎯 RESEARCH CAMPAIGN COMPLETE!")
        print(f"=" * 35)
        print(f"✅ Generations completed: {final_report['campaign_summary']['total_generations']}")
        print(f"✅ Best fitness achieved: {final_report['campaign_summary']['best_fitness_achieved']:.4f}")
        print(f"✅ Algorithms evaluated: {final_report['campaign_summary']['total_algorithms_evaluated']}")
        print(f"✅ Final report: {report_file}")
        print(f"✅ Best algorithm: algorithm_research/results/best_algorithm.py")

def main():
    """Main function"""
    
    # Initialize researcher
    researcher = AlgorithmResearcher()
    
    # Run research campaign
    researcher.run_research_campaign(target_generations=20)

if __name__ == "__main__":
    main()
