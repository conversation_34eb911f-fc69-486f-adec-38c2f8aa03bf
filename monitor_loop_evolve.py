#!/usr/bin/env python3
"""
LOOP EVOLVE SYSTEM MONITOR
=========================

Real-time monitoring of the Loop Evolve system's operation,
showing evolution progress, fitness scores, and system metrics.
"""

import asyncio
import logging
import time
from datetime import datetime
import json
import os
from typing import Dict, List, Optional
import yaml
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn

from loop_openevolve_system import LoopController
from loop_openevolve_complete import CompleteEvaluatorPool

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('loop_evolve_monitor.log')
    ]
)
logger = logging.getLogger(__name__)

console = Console()

class LoopEvolveMonitor:
    """Monitor for the Loop Evolve system"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.controller: Optional[LoopController] = None
        self.generation = 0
        self.best_fitness = 0.0
        self.population_stats = {
            'size': 0,
            'avg_fitness': 0.0,
            'min_fitness': 0.0,
            'max_fitness': 0.0
        }
        self.evolution_stats = {
            'start_time': None,
            'elapsed_time': 0,
            'generations_completed': 0,
            'total_evaluations': 0,
            'successful_mutations': 0,
            'failed_mutations': 0
        }
        self.rate_limit_stats = {
            'requests_today': 0,
            'tokens_used': 0,
            'remaining_requests': 25,
            'remaining_tokens': 250000
        }
    
    async def initialize(self):
        """Initialize the monitor and controller"""
        try:
            # Load configuration
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Initialize controller
            self.controller = LoopController(config)
            self.evolution_stats['start_time'] = datetime.now()
            
            console.print("[bold green]✓[/] Loop Evolve Monitor initialized")
            
        except Exception as e:
            console.print(f"[bold red]✗[/] Failed to initialize monitor: {e}")
            raise
    
    def update_stats(self):
        """Update monitoring statistics"""
        if not self.controller:
            return
        
        # Update population stats
        programs = self.controller.program_db.programs
        if programs:
            self.population_stats['size'] = len(programs)
            self.population_stats['avg_fitness'] = sum(p.fitness for p in programs) / len(programs)
            self.population_stats['min_fitness'] = min(p.fitness for p in programs)
            self.population_stats['max_fitness'] = max(p.fitness for p in programs)
        
        # Update evolution stats
        if self.evolution_stats['start_time']:
            self.evolution_stats['elapsed_time'] = (datetime.now() - self.evolution_stats['start_time']).total_seconds()
        
        # Update rate limit stats
        self.rate_limit_stats['requests_today'] = self.controller.llm_ensemble.requests_today
        self.rate_limit_stats['tokens_used'] = self.controller.llm_ensemble.tokens_used
        self.rate_limit_stats['remaining_requests'] = 25 - self.rate_limit_stats['requests_today']
        self.rate_limit_stats['remaining_tokens'] = 250000 - self.rate_limit_stats['tokens_used']
    
    def create_status_table(self) -> Table:
        """Create a table showing current system status"""
        table = Table(title="Loop Evolve System Status")
        
        # Add columns
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        # Add rows
        table.add_row("Generation", str(self.generation))
        table.add_row("Best Fitness", f"{self.best_fitness:.4f}")
        table.add_row("Population Size", str(self.population_stats['size']))
        table.add_row("Avg Fitness", f"{self.population_stats['avg_fitness']:.4f}")
        table.add_row("Min Fitness", f"{self.population_stats['min_fitness']:.4f}")
        table.add_row("Max Fitness", f"{self.population_stats['max_fitness']:.4f}")
        table.add_row("Elapsed Time", f"{self.evolution_stats['elapsed_time']:.1f}s")
        table.add_row("Requests Today", f"{self.rate_limit_stats['requests_today']}/25")
        table.add_row("Tokens Used", f"{self.rate_limit_stats['tokens_used']}/250000")
        
        return table
    
    def create_evolution_table(self) -> Table:
        """Create a table showing evolution statistics"""
        table = Table(title="Evolution Statistics")
        
        # Add columns
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        # Add rows
        table.add_row("Generations Completed", str(self.evolution_stats['generations_completed']))
        table.add_row("Total Evaluations", str(self.evolution_stats['total_evaluations']))
        table.add_row("Successful Mutations", str(self.evolution_stats['successful_mutations']))
        table.add_row("Failed Mutations", str(self.evolution_stats['failed_mutations']))
        table.add_row("Success Rate", f"{(self.evolution_stats['successful_mutations'] / (self.evolution_stats['successful_mutations'] + self.evolution_stats['failed_mutations']) * 100):.1f}%")
        
        return table
    
    async def monitor_evolution(self, generations: int = 10):
        """Monitor the evolution process"""
        if not self.controller:
            await self.initialize()
        
        with Live(
            Panel("Initializing...", title="Loop Evolve Monitor"),
            refresh_per_second=4
        ) as live:
            for gen in range(generations):
                self.generation = gen + 1
                
                # Update display
                live.update(
                    Panel(
                        self.create_status_table(),
                        title=f"Generation {self.generation}/{generations}"
                    )
                )
                
                # Run evolution
                try:
                    await self.controller.evolve_generation()
                    self.evolution_stats['generations_completed'] += 1
                    
                    # Update best fitness
                    best_program = self.controller.get_best_program()
                    if best_program and best_program.fitness > self.best_fitness:
                        self.best_fitness = best_program.fitness
                    
                    # Update stats
                    self.update_stats()
                    
                    # Show detailed stats every 5 generations
                    if (gen + 1) % 5 == 0:
                        console.print(self.create_evolution_table())
                    
                except Exception as e:
                    console.print(f"[bold red]Error in generation {self.generation}: {e}[/]")
                
                # Small delay to prevent overwhelming the display
                await asyncio.sleep(0.1)
    
    def save_checkpoint(self):
        """Save current state to a checkpoint file"""
        checkpoint = {
            'generation': self.generation,
            'best_fitness': self.best_fitness,
            'population_stats': self.population_stats,
            'evolution_stats': self.evolution_stats,
            'rate_limit_stats': self.rate_limit_stats,
            'timestamp': datetime.now().isoformat()
        }
        
        os.makedirs('checkpoints', exist_ok=True)
        with open(f'checkpoints/checkpoint_gen_{self.generation}.json', 'w') as f:
            json.dump(checkpoint, f, indent=2)
        
        console.print(f"[bold green]✓[/] Checkpoint saved for generation {self.generation}")

async def main():
    """Main function"""
    console.print("[bold blue]Loop Evolve System Monitor[/]")
    console.print("=" * 50)
    
    monitor = LoopEvolveMonitor()
    
    try:
        await monitor.initialize()
        await monitor.monitor_evolution(generations=10)
        monitor.save_checkpoint()
        
    except KeyboardInterrupt:
        console.print("\n[bold yellow]Monitoring interrupted by user[/]")
        monitor.save_checkpoint()
    
    except Exception as e:
        console.print(f"[bold red]Error: {e}[/]")
    
    finally:
        console.print("\n[bold green]Monitoring complete[/]")

if __name__ == "__main__":
    asyncio.run(main()) 