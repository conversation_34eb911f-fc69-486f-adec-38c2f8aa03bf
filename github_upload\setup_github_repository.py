#!/usr/bin/env python3
"""
🔥 GITHUB REPOSITORY SETUP
==========================

Setup GitHub repository for breakthrough compression algorithm
Upload real algorithm code and documentation
"""

import os
import shutil
import json
from datetime import datetime

def setup_github_repository():
    """Setup GitHub repository structure for breakthrough algorithm"""
    
    print("🔥🔥🔥 SETTING UP GITHUB REPOSITORY 🔥🔥🔥")
    print("=" * 60)
    print("🎯 GOAL: Upload breakthrough compression algorithm to GitHub")
    print("⚡ STATUS: Creating repository structure")
    print("=" * 60)
    
    # Create main repository directory
    repo_name = "breakthrough-compression-algorithm"
    
    if os.path.exists(repo_name):
        print(f"📁 Removing existing directory: {repo_name}")
        shutil.rmtree(repo_name)
    
    os.makedirs(repo_name, exist_ok=True)
    print(f"📁 Created repository: {repo_name}/")
    
    # Create repository structure
    subdirs = [
        "src",              # Source code
        "examples",         # Usage examples
        "tests",           # Test files
        "docs",            # Documentation
        "results",         # Experimental results
        "benchmarks"       # Performance benchmarks
    ]
    
    for subdir in subdirs:
        subdir_path = os.path.join(repo_name, subdir)
        os.makedirs(subdir_path, exist_ok=True)
        print(f"   📂 Created: {subdir}/")
    
    # Copy algorithm files
    print(f"\n📋 COPYING ALGORITHM FILES:")
    
    # Main algorithm files
    algorithm_files = [
        ("recursive_self_reference_algorithm.py", "src/"),
        ("breakthrough_recursive_compressor.py", "src/"),
        ("real_1gb_to_8kb_compression.py", "examples/"),
        ("mistral_7b_file_compression.py", "examples/"),
        ("real_breakthrough_implementation.py", "src/")
    ]
    
    for source_file, dest_dir in algorithm_files:
        if os.path.exists(source_file):
            dest_path = os.path.join(repo_name, dest_dir, source_file)
            shutil.copy2(source_file, dest_path)
            print(f"   ✅ Copied: {source_file} → {dest_dir}")
        elif os.path.exists(f"breakthrough_compression_research_paper/code/{source_file}"):
            source_path = f"breakthrough_compression_research_paper/code/{source_file}"
            dest_path = os.path.join(repo_name, dest_dir, source_file)
            shutil.copy2(source_path, dest_path)
            print(f"   ✅ Copied: {source_path} → {dest_dir}")
    
    # Copy result files
    result_files = [
        "large_scale_test_results_20250617_122823.json",
        "breakthrough_algorithm_results_20250617_122459.json"
    ]
    
    for result_file in result_files:
        if os.path.exists(result_file):
            dest_path = os.path.join(repo_name, "results", result_file)
            shutil.copy2(result_file, dest_path)
            print(f"   ✅ Copied: {result_file} → results/")
    
    # Copy compressed model results
    if os.path.exists("mistral_7b_compressed"):
        dest_dir = os.path.join(repo_name, "results", "mistral_7b_compressed")
        shutil.copytree("mistral_7b_compressed", dest_dir)
        print(f"   ✅ Copied: mistral_7b_compressed/ → results/")
    
    # Copy research paper
    if os.path.exists("breakthrough_compression_research_paper/paper/breakthrough_compression_algorithm.md"):
        dest_path = os.path.join(repo_name, "docs", "research_paper.md")
        shutil.copy2("breakthrough_compression_research_paper/paper/breakthrough_compression_algorithm.md", dest_path)
        print(f"   ✅ Copied: research_paper.md → docs/")
    
    print(f"\n✅ Repository structure created successfully!")
    print(f"📁 Repository: {repo_name}/")
    print(f"🚀 Ready for GitHub upload")
    
    return repo_name

if __name__ == "__main__":
    setup_github_repository()
