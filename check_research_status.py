#!/usr/bin/env python3
"""
🔥 LOOP RESEARCH SYSTEM STATUS CHECK
===================================

Quick status check for the Loop algorithm research system.
"""

import os
import yaml
import json
from pathlib import Path

def check_system_status():
    """Check if the research system is ready"""
    
    print("🔥 LOOP ALGORITHM RESEARCH SYSTEM STATUS")
    print("=" * 50)
    
    # Check configuration
    config_file = "algorithm_research_config.yaml"
    if os.path.exists(config_file):
        print("✅ Configuration file: READY")
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        print(f"   Research focus: {config.get('research_focus', 'Unknown')}")
        print(f"   Population size: {config.get('evolution', {}).get('population_size', 'Unknown')}")
        print(f"   Target domains: {len(config.get('target_domains', []))}")
    else:
        print("❌ Configuration file: MISSING")
        return False
    
    # Check directories
    required_dirs = [
        "algorithm_research",
        "algorithm_research/programs", 
        "algorithm_research/results",
        "algorithm_research/logs",
        "algorithm_research/checkpoints"
    ]
    
    print(f"\n📁 Directory structure:")
    all_dirs_exist = True
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✅ {directory}")
        else:
            print(f"❌ {directory}")
            all_dirs_exist = False
    
    # Check starter algorithms
    print(f"\n🧬 Starter algorithms:")
    starter_files = [
        "algorithm_research/programs/compression_starter.py",
        "algorithm_research/programs/streaming_starter.py", 
        "algorithm_research/programs/optimization_starter.py"
    ]
    
    algorithms_ready = True
    for file_path in starter_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {os.path.basename(file_path)} ({size} bytes)")
        else:
            print(f"❌ {os.path.basename(file_path)}")
            algorithms_ready = False
    
    # Check sessions
    sessions_dir = Path("algorithm_research/sessions")
    if sessions_dir.exists():
        sessions = list(sessions_dir.iterdir())
        print(f"\n🚀 Research sessions: {len(sessions)} created")
        if sessions:
            latest_session = max(sessions, key=lambda x: x.stat().st_mtime)
            print(f"   Latest: {latest_session.name}")
    else:
        print(f"\n❌ No research sessions found")
    
    # Overall status
    print(f"\n🎯 OVERALL STATUS:")
    if all_dirs_exist and algorithms_ready:
        print("✅ SYSTEM READY FOR ALGORITHM RESEARCH")
        print("\nNext steps:")
        print("1. Run: python run_algorithm_research.py")
        print("2. Monitor: algorithm_research/logs/")
        print("3. Results: algorithm_research/results/")
        return True
    else:
        print("❌ SYSTEM NOT READY")
        print("Run: python setup_algorithm_research.py")
        return False

def show_available_commands():
    """Show available research commands"""
    
    print(f"\n🔧 AVAILABLE COMMANDS:")
    print("=" * 25)
    
    commands = [
        ("python setup_algorithm_research.py", "Initialize the research system"),
        ("python run_algorithm_research.py", "Start algorithm evolution"),
        ("python check_research_status.py", "Check system status"),
        ("python view_research_results.py", "View research results")
    ]
    
    for command, description in commands:
        status = "✅" if os.path.exists(command.split()[1]) else "❌"
        print(f"{status} {command}")
        print(f"   {description}")

if __name__ == "__main__":
    system_ready = check_system_status()
    show_available_commands()
    
    if system_ready:
        print(f"\n🔥 READY TO RESEARCH ALGORITHMS!")
    else:
        print(f"\n⚠️  Please run setup first.")
