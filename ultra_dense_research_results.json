[{"average_compression_ratio": 0.0, "progress_to_target": 0.0, "test_results": [{"size": 1024, "compression_ratio": 0, "error": "Object of type uint8 is not JSON serializable"}, {"size": 4096, "compression_ratio": 0, "error": "Object of type uint8 is not JSON serializable"}, {"size": 16384, "compression_ratio": 0, "error": "Object of type uint8 is not JSON serializable"}], "algorithm_name": "Kolmogorov Minimal Program"}, {"average_compression_ratio": 8.627259279329964, "progress_to_target": 0.006582076476539585, "test_results": [{"size": 1024, "compression_ratio": 1.2248803827751196, "encoding_time": 0.02465677261352539, "method": "quantum_entanglement", "metadata": {"entangled_pairs": 50, "compression_efficiency": 1.2248803827751196}}, {"size": 4096, "compression_ratio": 4.940892641737032, "encoding_time": 0.016066312789916992, "method": "quantum_entanglement", "metadata": {"entangled_pairs": 50, "compression_efficiency": 4.940892641737032}}, {"size": 16384, "compression_ratio": 19.71600481347774, "encoding_time": 0.07443690299987793, "method": "quantum_entanglement", "metadata": {"entangled_pairs": 50, "compression_efficiency": 19.71600481347774}}], "algorithm_name": "Quantum Entanglement Encoding"}, {"average_compression_ratio": 18.337429480337864, "progress_to_target": 0.013990348419447223, "test_results": [{"size": 1024, "compression_ratio": 2.632390745501285, "encoding_time": 0.008679628372192383, "method": "recursive_isomorphism", "metadata": {"recursive_depth": 0, "isomorphism_complexity": 0}}, {"size": 4096, "compression_ratio": 10.583979328165375, "encoding_time": 0.0, "method": "recursive_isomorphism", "metadata": {"recursive_depth": 0, "isomorphism_complexity": 0}}, {"size": 16384, "compression_ratio": 41.795918367346935, "encoding_time": 0.008541345596313477, "method": "recursive_isomorphism", "metadata": {"recursive_depth": 0, "isomorphism_complexity": 0}}], "algorithm_name": "Recursive Isomorphism"}, {"average_compression_ratio": 4.128581304209383, "progress_to_target": 0.0031498575624156057, "test_results": [{"size": 1024, "compression_ratio": 0.5912240184757506, "encoding_time": 0.016508102416992188, "method": "time_as_data", "metadata": {"temporal_channels": 3, "encoding_efficiency": 0.5912240184757506}}, {"size": 4096, "compression_ratio": 2.3567318757192175, "encoding_time": 0.0, "method": "time_as_data", "metadata": {"temporal_channels": 3, "encoding_efficiency": 2.3567318757192175}}, {"size": 16384, "compression_ratio": 9.43778801843318, "encoding_time": 0.0, "method": "time_as_data", "metadata": {"temporal_channels": 3, "encoding_efficiency": 9.43778801843318}}], "algorithm_name": "Time-as-Data Encoding"}]