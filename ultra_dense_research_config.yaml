research_focus: "ultra_dense_data_representation"
target_compression: "1GB_to_8KB"  # 131,072x compression ratio
target_ratio: 131072

research_domains:
  mathematics:
    - "non_integer_bases"
    - "p_adic_numbers"
    - "fractal_indexing"
    - "godel_encoding"
    - "kolmogorov_minimal_programs"
    - "tensor_spaces"
    - "clifford_algebras"
    
  physics:
    - "holographic_principles"
    - "entanglement_bit_packing"
    - "phase_based_storage"
    - "wave_interference_storage"
    - "time_as_data"
    - "ads_cft_encoding"
    
  biology:
    - "dna_folding"
    - "protein_conformation"
    - "genetic_encoding"
    - "epigenetic_switches"
    - "codon_symbolic"
    
  information_theory:
    - "beyond_shannon_entropy"
    - "recursive_patterns"
    - "stackable_codebooks"
    - "recursive_isomorphisms"
    
  computer_science:
    - "reversible_languages"
    - "self_modifying_code"
    - "computational_holography"
    - "interference_patterns"

constraints:
  no_neural_networks: true
  no_llms: true
  no_pretrained_models: true
  no_probabilistic_reconstruction: true
  no_hallucination: true
  purely_algorithmic: true
  information_preservation: "full_or_functionally_complete"

evaluation_criteria:
  compression_ratio: 0.4  # Weight for achieving target ratio
  information_preservation: 0.3  # Weight for data integrity
  algorithmic_originality: 0.2  # Weight for novelty
  scientific_plausibility: 0.1  # Weight for theoretical soundness

llm:
  default_provider: "gemini"
  providers:
    gemini:
      model: "gemini-2.0-flash-exp"
      temperature: 0.8  # Higher for creativity
      max_tokens: 8192
      rate_limit_delay: 2  # 2 seconds between calls

evolution:
  population_size: 15
  generations: 50
  selection_rate: 0.2
  mutation_rate: 0.8
  crossover_rate: 0.6
  elite_preservation: 0.1

evaluation:
  timeout_seconds: 120
  max_memory_mb: 2048
  test_data_sizes: [1024, 4096, 16384, 65536]  # Test on smaller sizes first
  target_test_size: **********  # 1GB
  target_output_size: 8192  # 8KB

algorithm_templates:
  mathematical_encoding: |
    def ultra_dense_encode(data_bytes, method="fractal_indexing"):
        '''
        Ultra-dense mathematical encoding algorithm
        
        Args:
            data_bytes: Input data as bytes (up to 1GB)
            method: Mathematical encoding method
            
        Returns:
            dict: {
                'encoded': encoded representation (max 8KB),
                'compression_ratio': actual ratio achieved,
                'method': encoding method used,
                'metadata': reconstruction metadata
            }
        '''
        # Implement mathematical encoding here
        pass
    
    def ultra_dense_decode(encoded_data, metadata):
        '''Decode ultra-dense representation back to original data'''
        # Implement mathematical decoding here
        pass
    
    def evaluate():
        '''Test the ultra-dense encoding algorithm'''
        import os
        import random
        import hashlib
        
        # Generate test data of various sizes
        test_sizes = [1024, 4096, 16384, 65536]
        results = []
        
        for size in test_sizes:
            # Create test data
            test_data = os.urandom(size)
            original_hash = hashlib.sha256(test_data).hexdigest()
            
            try:
                # Encode
                encoded = ultra_dense_encode(test_data)
                encoded_size = len(str(encoded['encoded']).encode())
                
                # Decode
                decoded = ultra_dense_decode(encoded['encoded'], encoded['metadata'])
                decoded_hash = hashlib.sha256(decoded).hexdigest()
                
                # Calculate metrics
                compression_ratio = size / encoded_size if encoded_size > 0 else 0
                data_integrity = 1.0 if original_hash == decoded_hash else 0.0
                
                results.append({
                    'size': size,
                    'compression_ratio': compression_ratio,
                    'data_integrity': data_integrity,
                    'encoded_size': encoded_size
                })
                
            except Exception as e:
                results.append({
                    'size': size,
                    'compression_ratio': 0,
                    'data_integrity': 0,
                    'error': str(e)
                })
        
        # Calculate overall fitness
        if not results:
            return {'fitness': 0.0}
        
        avg_ratio = sum(r.get('compression_ratio', 0) for r in results) / len(results)
        avg_integrity = sum(r.get('data_integrity', 0) for r in results) / len(results)
        
        # Fitness combines compression ratio and data integrity
        fitness = (avg_ratio / 131072) * 0.7 + avg_integrity * 0.3
        
        return {
            'fitness': fitness,
            'compression_ratio': avg_ratio,
            'data_integrity': avg_integrity,
            'test_results': results
        }

  physics_encoding: |
    def holographic_encode(data_bytes, principle="ads_cft"):
        '''
        Physics-inspired holographic encoding
        
        Args:
            data_bytes: Input data
            principle: Physics principle to use
            
        Returns:
            dict: Holographic encoding result
        '''
        # Implement physics-based encoding
        pass
    
    def holographic_decode(encoded_data, metadata):
        '''Decode holographic representation'''
        # Implement physics-based decoding
        pass
    
    def evaluate():
        '''Evaluate physics-based encoding'''
        # Similar evaluation structure as mathematical
        pass

  biological_encoding: |
    def bio_encode(data_bytes, method="dna_folding"):
        '''
        Biology-inspired encoding (DNA, protein folding, etc.)
        
        Args:
            data_bytes: Input data
            method: Biological encoding method
            
        Returns:
            dict: Biological encoding result
        '''
        # Implement bio-inspired encoding
        pass
    
    def bio_decode(encoded_data, metadata):
        '''Decode biological representation'''
        # Implement bio-inspired decoding
        pass
    
    def evaluate():
        '''Evaluate biological encoding'''
        # Similar evaluation structure
        pass

prompt_templates:
  initial: |
    Create a novel ultra-dense data representation algorithm that can encode 1GB of data into 8KB.
    
    Domain: {domain}
    Approach: {approach}
    
    Constraints:
    - NO neural networks, LLMs, or pretrained models
    - NO probabilistic reconstruction or hallucination
    - Must be purely algorithmic and mathematical
    - Must preserve information (full or functionally complete)
    - Target compression ratio: 131,072x (1GB → 8KB)
    
    Focus on {domain} principles:
    {domain_principles}
    
    Create an algorithm that:
    1. Takes arbitrary data as input
    2. Produces a representation ≤8KB
    3. Can reconstruct the original data
    4. Uses novel {domain} concepts
    
    Template:
    {template}
    
    Be highly creative and scientifically rigorous. Think beyond conventional compression.

  evolution: |
    Improve this ultra-dense encoding algorithm based on these successful approaches:
    
    {best_programs}
    
    Current best compression ratio: {best_ratio}x
    Target: 131,072x (1GB → 8KB)
    
    Domain focus: {domain}
    
    Combine the best aspects and add novel improvements:
    - Better mathematical foundations
    - More efficient encoding schemes
    - Novel {domain} principles
    - Improved information preservation
    
    Create a superior algorithm that pushes closer to the target ratio.

domain_principles:
  mathematics: |
    - Non-integer number bases and p-adic representations
    - Gödel encoding and symbolic logic compression
    - Kolmogorov complexity and minimal programs
    - Fractal indexing and self-similar structures
    - Tensor spaces and Clifford algebras
    - Recursive mathematical structures
    
  physics: |
    - Holographic principle (boundary-bulk correspondence)
    - Quantum entanglement and bit-packing
    - Wave interference patterns for storage
    - Phase-based information encoding
    - Time as a data storage dimension
    - AdS/CFT correspondence principles
    
  biology: |
    - DNA folding and base-pair encoding
    - Protein conformation as information storage
    - Genetic code abstraction and codon mapping
    - Epigenetic switches for multi-layer encoding
    - Evolutionary compression strategies
    - Biological self-assembly principles
    
  information_theory: |
    - Beyond Shannon entropy models
    - Self-similar recursive pattern encoding
    - Stackable codebooks and recursive isomorphisms
    - Information-theoretic limits and breakthroughs
    - Novel entropy definitions
    - Recursive information structures
    
  computer_science: |
    - Reversible programming languages
    - Self-modifying code primitives
    - Computational holography models
    - Interference pattern computation
    - Recursive algorithm design
    - Novel data structure paradigms
